﻿@using PLRA.ESORS.Framework;
@using PLRA.ESORS.Portal.Razor.Components
@using PLRA.ESORS.Portal.Razor.Features.PartiesInfo;
@using PLRA.ESORS.Portal.Razor.Layout
@using PLRA.ESORS.Portal.ServiceContracts.Features.PartiesInfo;
@using Microsoft.AspNetCore.Components.Forms;
@inherits FormBase<PartiesInfoFormBusinessObject,PartiesInfoFormViewModel, long, IPartiesInfoFormDataService>

@if (SelectedItem != null)
{
    <StatusMessage Message="@(_message ?? ValidationError)" IsError="@(!string.IsNullOrEmpty(ValidationError))" />
    <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="@FormName">
        <DataAnnotationsValidator></DataAnnotationsValidator>
        <div class="border-0 card mb-4 p-5 rounded-4 shadow-sm">

            <div class="bg-white card-header p-0 pb-2">
                <div class="d-flex justify-content-between align-items-center">

                    <h5 class="mb-0">@Heading_En</h5>
                    <span class="urdu-text">@Heading_Ur</span>


                </div>
            </div>


            <div class="card-body" dir="rtl">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">قومی شناختی کارڈ نمبر</label>
                            <label class="form-label">
                                CNIC
                            </label>
                        </div>
                        <div class="input-group">
                            <InputNumber id="txtCNIC" @bind-Value="SelectedItem.Appealent_CNIC" maxlength="13" data-party="@PARTY_TYPE.ToString()" field-required />
                            <a class="btn btn-primary rounded-0 rounded-start urdu-text" id="btn-CNIC-Search">تلاش کریں</a>
                        </div>
                       @*  @if (IsFieldDisabled("cnic"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("cnic")</small>
                        } *@
                        
                        <ValidationMessage For="() => SelectedItem.Appealent_CNIC" class="text-danger" />

                    </div>
                    <div class="col-md-3">
                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">
                                نام
                            </label>
                            <label class="form-label">
                                Name
                            </label>
                        </div>
                        <InputText type="text" @bind-Value="SelectedItem.Appealent_Name" class="@GetFieldCssClass("name")" field-required disabled="@IsFieldDisabled("name")" />
                        <ValidationMessage For="() => SelectedItem.Appealent_Name" class="text-danger" />
                        @if (IsFieldDisabled("name"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("name")</small>
                        }
                    </div>
                    <div class="col-md-3">

                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">رشتہ</label>
                            <label class="form-label">
                                Relation
                            </label>
                        </div>
                        <InputText @bind-Value="SelectedItem.Appealent_Relation" class="@GetFieldCssClass("relation")" field-required disabled="@IsFieldDisabled("relation")" />
                        <ValidationMessage For="() => SelectedItem.Appealent_Relation" class="text-danger" />
                        @if (IsFieldDisabled("relation"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("relation")</small>
                        }

                    </div>
                    <div class="col-md-3">

                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">رشتہ دار کا نام</label>
                            <label class="form-label">
                                Relative Name
                            </label>
                        </div>
                        <InputText @bind-Value="SelectedItem.Appealent_RelativeName" class="@GetFieldCssClass("relativename")" field-required disabled="@IsFieldDisabled("relativename")" />
                        <ValidationMessage For="() => SelectedItem.Appealent_RelativeName" class="text-danger" />
                        @if (IsFieldDisabled("relativename"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("relativename")</small>
                        }

                    </div>
                    

                </div>

                <div class="row mb-3">
                    <div class="col-md-3">

                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">رابطہ</label>
                            <label class="form-label">
                                Mobile No
                            </label>
                        </div>
                        <InputNumber @bind-Value="SelectedItem.Appealent_PhoneNumber" maxlength="13" class="@GetFieldCssClass("phonenumber")" field-required disabled="@IsFieldDisabled("phonenumber")" />
                        <ValidationMessage For="() => SelectedItem.Appealent_PhoneNumber" class="text-danger" />
                        @if (IsFieldDisabled("phonenumber"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("phonenumber")</small>
                        }

                    </div>
                    <div class="col-md-3">
                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">ای میل</label>
                            <label class="form-label">
                                Email
                            </label>
                        </div>
                        <InputText @bind-Value="SelectedItem.Appealent_Email" class="@GetFieldCssClass("email")" field-required disabled="@IsFieldDisabled("email")" />
                        <ValidationMessage For="() => SelectedItem.Appealent_Email" class="text-danger" />
                        @if (IsFieldDisabled("email"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("email")</small>
                        }

                    </div>

                    <div class="col-md-6">
                        <div class="d-flex justify-content-between">
                            <label class="form-label urdu-text">پتہ</label>
                            <label class="form-label">
                                Address
                            </label>
                        </div>
                        <InputText @bind-Value="SelectedItem.Appealent_Address" class="@GetFieldCssClass("address")" field-required disabled="@IsFieldDisabled("address")" />
                        <ValidationMessage For="() => SelectedItem.Appealent_Address" class="text-danger" />
                        @if (IsFieldDisabled("address"))
                        {
                            <small class="text-muted">@GetFieldSourceMessage("address")</small>
                        }

                    </div>
                </div>

                @if (DisplayLawyerField)
                {
                    <div class="d-flex mb-3 row">
                        <div class="col-md-3">
                            <div class="d-flex justify-content-between">
                                <label class="form-label urdu-text">(وکیل کا نام (اختیاری</label>
                                <label class="form-label">
                                    Lawyer Name
                                </label>
                            </div>
                            <InputText @bind-Value="SelectedItem.Appealent_LawyerName" class="form-control urdu-text" />

                        </div>
                    </div>
                }



                <div class="row">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-success urdu-text" id="ApeallantSubmit">شامل کریں</button>
                    </div>
                </div>


            </div>
            <input type="hidden" id="hdnApealantSubmit" name="SelectedItem.Appealent_Id" value="@SelectedItem.Appealent_Id" />
        </div>
    </EditForm>

}