using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Portal.ServiceContracts.SelectList;
using System.Text.Json;

namespace PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo.Form
{
    public partial class PartiesInfoFormComponent
    {
        [Parameter]
        public long TaskId { get; set; }

        [Parameter]
        public int Step { get; set; }

        [Parameter]
        public PARTY_TYPE PARTY_TYPE { get; set; }

        [Parameter, EditorRequired]
        public string? Heading_En { get; set; }

        [Parameter, EditorRequired]
        public string? Heading_Ur { get; set; }

        [Parameter, EditorRequired]
        public bool DisplayLawyerField { get; set; } = true;

        [Parameter, EditorRequired]
        public string FormName { get; set; } = string.Empty;

        [Parameter]
        public bool FormHasListing { get; set; } = false;

        [SupplyParameterFromQuery(Name = "rowId")]
        public long RowId { get; set; } = 0;

        private string? _message;

        [SupplyParameterFromQuery(Name = "cnic")]
        public long CNIC { get; set; } = 0;

        [SupplyParameterFromQuery(Name = "partyType")]
        public string SELECTED_PARTY_TYPE { get; set; } = string.Empty;

        // Track which fields were populated from API (loaded from database JSON)
        private readonly Dictionary<string, bool> _fieldsFromApi = new()
        {
            ["name"] = false,
            ["relation"] = false,
            ["relativename"] = false,
            ["address"] = false,
            ["phonenumber"] = false,
            ["email"] = false,
            ["cnic"] = false
        };

        /// <summary>
        /// Model for storing API field metadata as JSON
        /// </summary>
        private class ApiFieldMetadata
        {
            public Dictionary<string, bool> FieldsFromApi { get; set; } = new();
            public long? LastApiCnic { get; set; }
        }

        protected override async Task OnInitializedAsync()
        {
            // Standard FormBase initialization
            Id = (long)PARTY_TYPE * 1_000_000_000_000 + (FormHasListing ? RowId : TaskId);
            var flagBit = 1L << 48;
            if (FormHasListing)
                Id |= flagBit;

            // Load existing data using FormBase GetItemById
            await base.OnInitializedAsync();

            if (SelectedItem != null)
            {
                SelectedItem.PARTY_TYPE = PARTY_TYPE;
                SelectedItem.Appealent_Id = SelectedItem.Appealent_Id ?? (FormHasListing ? RowId : 0);
                SelectedItem.EStampMainId = TaskId;

                // Load API field metadata from database JSON
                LoadApiFieldMetadataFromDatabase();

                // Handle CNIC search from query parameter (Case 2: CNIC Search Flow)
                if (!string.IsNullOrEmpty(SELECTED_PARTY_TYPE) &&
                    (PARTY_TYPE)Enum.Parse(typeof(PARTY_TYPE), SELECTED_PARTY_TYPE) == PARTY_TYPE &&
                    CNIC > 0)
                {
                    await PerformCnicSearchFromQuery(CNIC);
                }
            }
        }

        public override async Task BeforeSaveAsync()
        {
            SaveApiFieldMetadataToDatabase();
            await Task.CompletedTask;
        }

        public override async Task OnAfterSaveAsync(long key)
        {
            ArgumentNullException.ThrowIfNull(SelectedItem);

            string url = string.Empty;
            if (key > 0)
            {
                SelectedItem.Appealent_Id = key;
                _message = "Record has been saved successfully";
                url = NavigationManager?.AddQueryString(new { partyType = PARTY_TYPE })!;
            }
            if (FormHasListing)
            {
                SelectedItem = await CreateSelectedItem();
                url = NavigationManager?.AddQueryString(new { reloadparty = true, partyType = PARTY_TYPE })!;
            }

            NavigationManager?.NavigateTo(url);
        }

        /// <summary>
        /// Performs CNIC search triggered by query parameter from JavaScript redirect
        /// Uses database-only approach with FormBase SaveAsync/GetItemById
        /// </summary>
        /// <param name="cnicToSearch">CNIC number to search</param>
        private async Task PerformCnicSearchFromQuery(long cnicToSearch)
        {
            try
            {
                if (cnicToSearch <= 0 || SelectedItem == null)
                {
                    ValidationError = "Please enter a valid CNIC number";
                    return;
                }

                // Clear previous API field states if searching different CNIC
                var metadata = LoadApiFieldMetadataFromDatabase();
                if (metadata?.LastApiCnic.HasValue == true && metadata.LastApiCnic.Value != cnicToSearch)
                {
                    ClearApiFieldStates();
                }

                // Call API to get person details
                using var scope = ScopeFactory?.CreateScope();
                var service = scope?.ServiceProvider.GetRequiredService<ISelectListDataService>();
                ArgumentNullException.ThrowIfNull(service);

                var person = await service.GetPersonDetail(cnicToSearch);
                if (person != null)
                {
                    // Update form fields with API data and mark them as disabled
                    UpdateFieldsFromApiData(person, cnicToSearch);
                    ValidationError = null;
                    _message = "Record found and data populated from API";
                }
                else
                {
                    // Clear SelectedItem fields when no person found
                    ClearSelectedItemFields();
                    ValidationError = $"No record found for CNIC: {cnicToSearch}";
                    _message = null;
                }
            }
            catch (Exception ex)
            {
                ValidationError = $"Error searching CNIC: {ex.Message}";
                _message = null;
            }
        }

        /// <summary>
        /// Updates form fields with API data and marks them as disabled
        /// </summary>
        /// <param name="person">Person data from API</param>
        /// <param name="cnicToSearch">CNIC that was searched</param>
        private void UpdateFieldsFromApiData(dynamic person, long cnicToSearch)
        {
            // Update CNIC
            if (!string.IsNullOrEmpty((string)person.CNIC))
            {
                SelectedItem.Appealent_CNIC = Convert.ToInt64((string)person.CNIC);
            }

            // Update fields with API data and mark them as disabled
            if (!string.IsNullOrEmpty((string)person.FirstName))
            {
                SelectedItem.Appealent_Name = (string)person.FirstName;
                _fieldsFromApi["name"] = true;
            }

            if (!string.IsNullOrEmpty((string)person.Relation))
            {
                SelectedItem.Appealent_Relation = (string)person.Relation;
                _fieldsFromApi["relation"] = true;
            }

            if (!string.IsNullOrEmpty((string)person.RelativeName))
            {
                SelectedItem.Appealent_RelativeName = (string)person.RelativeName;
                _fieldsFromApi["relativename"] = true;
            }

            if (!string.IsNullOrEmpty((string)person.Address))
            {
                SelectedItem.Appealent_Address = (string)person.Address;
                _fieldsFromApi["address"] = true;
            }

            if (!string.IsNullOrEmpty((string)person.Email))
            {
                SelectedItem.Appealent_Email = (string)person.Email;
                _fieldsFromApi["email"] = true;
            }

            if (!string.IsNullOrEmpty((string)person.Phone) && long.TryParse((string)person.Phone, out long phoneNumber))
            {
                SelectedItem.Appealent_PhoneNumber = phoneNumber;
                _fieldsFromApi["phonenumber"] = true;
            }
        }

        /// <summary>
        /// Loads API field metadata from database JSON and restores field states
        /// </summary>
        private ApiFieldMetadata? LoadApiFieldMetadataFromDatabase()
        {
            if (SelectedItem?.ApiFieldMetadata == null)
                return null;

            try
            {
                var metadata = JsonSerializer.Deserialize<ApiFieldMetadata>(SelectedItem.ApiFieldMetadata);
                if (metadata?.FieldsFromApi != null)
                {
                    // Restore field states from database
                    foreach (var field in metadata.FieldsFromApi)
                    {
                        if (_fieldsFromApi.ContainsKey(field.Key))
                        {
                            _fieldsFromApi[field.Key] = field.Value;
                        }
                    }
                }
                return metadata;
            }
            catch (JsonException)
            {
                // If JSON is invalid, treat as no metadata
                return null;
            }
        }

        /// <summary>
        /// Saves current API field states to database as JSON
        /// </summary>
        private void SaveApiFieldMetadataToDatabase()
        {
            if (SelectedItem == null) return;

            var metadata = new ApiFieldMetadata
            {
                FieldsFromApi = new Dictionary<string, bool>(_fieldsFromApi),
                LastApiCnic = SelectedItem.Appealent_CNIC
            };

            SelectedItem.ApiFieldMetadata = JsonSerializer.Serialize(metadata);
        }

        /// <summary>
        /// Clears all API field states (used when searching different CNIC)
        /// </summary>
        private void ClearApiFieldStates()
        {
            foreach (var key in _fieldsFromApi.Keys.ToList())
            {
                _fieldsFromApi[key] = false;
            }
        }

        /// <summary>
        /// Clears SelectedItem fields when no person found in API search
        /// </summary>
        private void ClearSelectedItemFields()
        {
            if (SelectedItem == null) return;

            // Clear all form fields except CNIC (keep the searched CNIC)
            SelectedItem.Appealent_Name = null;
            SelectedItem.Appealent_Relation = null;
            SelectedItem.Appealent_RelativeName = null;
            SelectedItem.Appealent_Address = null;
            SelectedItem.Appealent_Email = null;
            SelectedItem.Appealent_PhoneNumber = null;
            SelectedItem.Appealent_CNIC = null;

            // Clear API field states since no data was found
            ClearApiFieldStates();
        }

        /// <summary>
        /// Checks if a field should be disabled based on whether it was populated from API
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <returns>True if field should be disabled</returns>
        public bool IsFieldDisabled(string fieldName)
        {
            var key = fieldName.ToLower();
            return _fieldsFromApi.ContainsKey(key) && _fieldsFromApi[key];
        }

        /// <summary>
        /// Gets CSS class for disabled fields
        /// </summary>
        /// <param name="fieldName">Name of the field</param>
        /// <returns>CSS class string</returns>
        public string GetFieldCssClass(string fieldName)
        {
            var baseClass = "form-control";
            if (fieldName == "name" || fieldName == "relation" || fieldName == "relativename" || fieldName == "address")
            {
                baseClass += " urdu-text";
            }

            if (IsFieldDisabled(fieldName))
            {
                baseClass += " bg-light text-muted";
            }

            return baseClass;
        }

        /// <summary>
        /// Gets a message indicating the source of field data
        /// </summary>
        /// <param name="fieldName">Name of the field</param>
        /// <returns>Message string or null</returns>
        public string? GetFieldSourceMessage(string fieldName)
        {
            if (IsFieldDisabled(fieldName))
            {
                return "This field was populated from API and is disabled";
            }
            return null;
        }




    }
}