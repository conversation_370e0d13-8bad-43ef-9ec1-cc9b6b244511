# PLRA.ESORS Error Logging System - Implementation Guide

## 🎯 **Overview**

The PLRA.ESORS Error Logging System provides comprehensive database-driven error logging capabilities for both Admin and Portal projects. The system captures detailed contextual information including user details, HTTP context, component information, and custom data for effective debugging and monitoring.

## 🏗️ **Architecture Components**

### **1. Database Layer**
- **ErrorLog Entity**: Comprehensive error storage with 20+ properties
- **Database Migration**: `AddErrorLoggingSystem` migration created
- **Sequence**: `ErrorLogSequence` for auto-incrementing IDs

### **2. Service Layer**
- **IErrorLoggingService**: Interface with 15+ specialized logging methods
- **ErrorLoggingServerSideDataService**: Implementation for both Admin and Portal
- **Dependency Injection**: Registered in both project ServiceRegistrars

### **3. Middleware Layer**
- **GlobalExceptionHandlingMiddleware**: Catches unhandled exceptions
- **Automatic Logging**: Critical errors logged with full context
- **JSON Response**: Structured error responses for API calls

## 📊 **ErrorLog Entity Properties**

```csharp
public class ErrorLog : BaseEntity
{
    public long Id { get; set; }                    // Auto-increment primary key
    public DateTime Timestamp { get; set; }         // UTC timestamp
    public string? UserId { get; set; }             // Current user ID
    public string? UserName { get; set; }           // Current username
    public string ComponentName { get; set; }       // Component/class name
    public string MethodName { get; set; }          // Method name
    public string SeverityLevel { get; set; }       // Critical/Error/Warning/Information
    public string ErrorMessage { get; set; }        // Primary error message
    public string? StackTrace { get; set; }         // Full stack trace
    public string? InnerException { get; set; }     // Inner exception details
    public string? RequestPath { get; set; }        // HTTP request path
    public string? HttpMethod { get; set; }         // GET/POST/PUT/DELETE
    public string? UserAgent { get; set; }          // Browser/client info
    public string? IPAddress { get; set; }          // Client IP address
    public int? StatusCode { get; set; }            // HTTP status code
    public string? AdditionalData { get; set; }     // JSON serialized context data
    public string? CorrelationId { get; set; }      // Request trace ID
    public string? SessionId { get; set; }          // User session ID
    public long? TaskId { get; set; }               // Workflow task ID
    public long? WorkflowId { get; set; }           // Workflow ID
    public string? ErrorCategory { get; set; }      // Database/API/Validation/etc
    public bool IsResolved { get; set; }            // Resolution status
    public DateTime? ResolvedAt { get; set; }       // Resolution timestamp
    public string? ResolvedBy { get; set; }         // Resolver user ID
    public string? ResolutionNotes { get; set; }    // Resolution details
}
```

## 🔧 **Service Interface Methods**

### **General Logging Methods**
```csharp
Task LogErrorAsync(string componentName, string methodName, Exception exception, object? additionalData = null);
Task LogWarningAsync(string componentName, string methodName, string message, object? additionalData = null);
Task LogInformationAsync(string componentName, string methodName, string message, object? additionalData = null);
Task LogCriticalAsync(string componentName, string methodName, Exception exception, object? additionalData = null);
```

### **Specialized Logging Methods**
```csharp
Task LogDatabaseErrorAsync(string componentName, string methodName, Exception exception, string? sqlQuery = null, object? parameters = null);
Task LogApiErrorAsync(string componentName, string methodName, Exception exception, string? requestUrl = null, string? requestBody = null, string? responseBody = null);
Task LogValidationErrorAsync(string componentName, string methodName, string validationMessage, object? invalidData = null);
Task LogAuthenticationErrorAsync(string componentName, string methodName, string errorMessage, string? userId = null, string? attemptedAction = null);
Task LogFileOperationErrorAsync(string componentName, string methodName, Exception exception, string? filePath = null, string? operation = null);
Task LogExternalServiceErrorAsync(string componentName, string methodName, Exception exception, string? serviceName = null, string? serviceEndpoint = null);
Task LogWorkflowErrorAsync(string componentName, string methodName, Exception exception, long? taskId = null, long? workflowId = null, string? workflowStep = null);
```

### **Management Methods**
```csharp
Task<IEnumerable<ErrorLog>> GetErrorLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? severityLevel = null, string? componentName = null, int skip = 0, int take = 100);
Task MarkErrorAsResolvedAsync(long errorLogId, string resolutionNotes);
Task<ErrorLogStatistics> GetErrorStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
```

## 💻 **Usage Examples**

### **1. Basic Error Logging in DataService**
```csharp
public class WorkflowFormDataService
{
    private readonly IErrorLoggingService _errorLoggingService;
    
    public async Task<WorkflowFormBusinessObject> SaveAsync(WorkflowFormBusinessObject model)
    {
        try
        {
            // Your business logic here
            return await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            await _errorLoggingService.LogErrorAsync(
                nameof(WorkflowFormDataService),
                nameof(SaveAsync),
                ex,
                new { WorkflowId = model.WorkflowId, FormData = model }
            );
            throw; // Re-throw to maintain normal error flow
        }
    }
}
```

### **2. Database Error Logging**
```csharp
public async Task<List<Conveyance23>> GetConveyanceDataAsync(int workflowId)
{
    try
    {
        var query = "SELECT * FROM Conveyance23 WHERE WorkflowId = @workflowId";
        return await _context.Conveyance23s.FromSqlRaw(query, workflowId).ToListAsync();
    }
    catch (SqlException ex)
    {
        await _errorLoggingService.LogDatabaseErrorAsync(
            nameof(ConveyanceDataService),
            nameof(GetConveyanceDataAsync),
            ex,
            query,
            new { workflowId }
        );
        throw;
    }
}
```

### **3. API Error Logging**
```csharp
public async Task<PaymentResponse> ProcessPaymentAsync(PaymentRequest request)
{
    try
    {
        var response = await _httpClient.PostAsJsonAsync("/api/payment", request);
        return await response.Content.ReadFromJsonAsync<PaymentResponse>();
    }
    catch (HttpRequestException ex)
    {
        await _errorLoggingService.LogApiErrorAsync(
            nameof(PaymentService),
            nameof(ProcessPaymentAsync),
            ex,
            "/api/payment",
            JsonSerializer.Serialize(request),
            null
        );
        throw;
    }
}
```

### **4. Validation Error Logging**
```csharp
public async Task<ValidationResult> ValidatePartiesInfoAsync(PartiesInfoModel model)
{
    if (string.IsNullOrEmpty(model.CNIC))
    {
        await _errorLoggingService.LogValidationErrorAsync(
            nameof(PartiesInfoValidator),
            nameof(ValidatePartiesInfoAsync),
            "CNIC is required for party validation",
            model
        );
        return ValidationResult.Failed("CNIC is required");
    }
    
    return ValidationResult.Success();
}
```

### **5. Workflow Error Logging**
```csharp
public async Task ExecuteWorkflowStepAsync(long taskId, long workflowId, string stepName)
{
    try
    {
        // Execute workflow step logic
        await ProcessWorkflowStep(taskId, stepName);
    }
    catch (Exception ex)
    {
        await _errorLoggingService.LogWorkflowErrorAsync(
            nameof(WorkflowExecutionService),
            nameof(ExecuteWorkflowStepAsync),
            ex,
            taskId,
            workflowId,
            stepName
        );
        throw;
    }
}
```

### **6. Razor Component Error Logging**
```csharp
@inject IErrorLoggingService ErrorLoggingService

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            await LoadDataAsync();
        }
        catch (Exception ex)
        {
            await ErrorLoggingService.LogErrorAsync(
                nameof(PartiesInfoFormComponent),
                nameof(OnInitializedAsync),
                ex,
                new { WorkflowId = WorkflowId, ComponentState = "Initializing" }
            );
            
            // Show user-friendly error message
            await ShowErrorMessage("Failed to load form data. Please try again.");
        }
    }
    
    private async Task HandleSubmitAsync()
    {
        try
        {
            await SaveFormDataAsync();
        }
        catch (Exception ex)
        {
            await ErrorLoggingService.LogErrorAsync(
                nameof(PartiesInfoFormComponent),
                nameof(HandleSubmitAsync),
                ex,
                new { FormData = Model, ValidationState = EditContext?.IsModified() }
            );
            
            await ShowErrorMessage("Failed to save form. Please check your data and try again.");
        }
    }
}
```

## 🔍 **Error Retrieval and Management**

### **Get Error Logs with Filtering**
```csharp
// Get recent critical errors
var criticalErrors = await _errorLoggingService.GetErrorLogsAsync(
    fromDate: DateTime.UtcNow.AddDays(-7),
    severityLevel: ErrorSeverityLevels.Critical,
    skip: 0,
    take: 50
);

// Get errors for specific component
var componentErrors = await _errorLoggingService.GetErrorLogsAsync(
    componentName: "PartiesInfoFormComponent",
    fromDate: DateTime.UtcNow.AddDays(-1)
);
```

### **Error Statistics for Dashboard**
```csharp
var stats = await _errorLoggingService.GetErrorStatisticsAsync(
    fromDate: DateTime.UtcNow.AddDays(-30)
);

Console.WriteLine($"Total Errors: {stats.TotalErrors}");
Console.WriteLine($"Critical Errors: {stats.CriticalErrors}");
Console.WriteLine($"Unresolved Errors: {stats.UnresolvedErrors}");

// Errors by component
foreach (var component in stats.ErrorsByComponent)
{
    Console.WriteLine($"{component.Key}: {component.Value} errors");
}
```

### **Mark Errors as Resolved**
```csharp
await _errorLoggingService.MarkErrorAsResolvedAsync(
    errorLogId: 12345,
    resolutionNotes: "Fixed database connection timeout by increasing connection pool size"
);
```

## 🚀 **Deployment Steps**

### **1. Run Database Migration**
```bash
dotnet ef database update --project src/PLRA.ESORS.Server.Data --startup-project src/PLRA.ESORS.Server.WebApp/PLRA.ESORS.Server.WebApp
```

### **2. Verify Service Registration**
Both Admin and Portal projects have the error logging service registered:
- ✅ Admin: `PLRA.ESORS.Server.WebApp/ServiceRegistrar.cs`
- ✅ Portal: `PLRA.ESORS.Portal.Server.WebApp/ServiceRegistrar.cs`

### **3. Verify Middleware Registration**
Both projects have global exception handling middleware:
- ✅ Admin: `PLRA.ESORS.Server.WebApp/Program.cs`
- ✅ Portal: `PLRA.ESORS.Portal.Server.WebApp/Program.cs`

## 📈 **Best Practices**

### **1. Error Logging Guidelines**
- ✅ **Always log exceptions** in try-catch blocks
- ✅ **Include relevant context** in additionalData parameter
- ✅ **Use appropriate severity levels** (Critical/Error/Warning/Information)
- ✅ **Log before re-throwing** exceptions to maintain error flow
- ✅ **Include component and method names** for easy debugging

### **2. Performance Considerations**
- ✅ **Async logging** - All methods are async to prevent blocking
- ✅ **Fail-safe design** - Logging errors won't crash the application
- ✅ **Minimal overhead** - Logging happens in background
- ✅ **Structured data** - JSON serialization for complex objects

### **3. Security Considerations**
- ✅ **No sensitive data** in error messages (passwords, tokens, etc.)
- ✅ **IP address logging** for security analysis
- ✅ **User context** captured for audit trails
- ✅ **Correlation IDs** for request tracking

## 🎉 **Status: Production Ready**

The comprehensive error logging system is now fully implemented and ready for production use across both PLRA.ESORS Admin and Portal projects. The system provides:

- ✅ **Complete Database Infrastructure**: ErrorLog entity with migration
- ✅ **Comprehensive Service Layer**: 15+ specialized logging methods
- ✅ **Global Exception Handling**: Automatic critical error capture
- ✅ **Rich Context Capture**: User, HTTP, workflow, and custom data
- ✅ **Management Capabilities**: Error retrieval, statistics, and resolution tracking
- ✅ **Production Deployment**: Ready for immediate use

Start using the error logging system by injecting `IErrorLoggingService` into your components and wrapping critical operations in try-catch blocks with appropriate logging calls.
