<?xml version="1.0" encoding="UTF-8"?>
<!--Registration Data Transfer Interface	
Author:  <PERSON>
Date:  
Time: 11:00 
Version: 2.0
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
<xs:element name="BIOMETRIC_VERIFICATION" nillable="false">
<xs:complexType>
<xs:sequence>
<xs:element name="RESPONSE_DATA" nillable="false">
<xs:complexType>
<xs:sequence>
<xs:element name="RESPONSE_STATUS" nillable="false">
	<xs:complexType>
		<xs:sequence>
			<xs:element name="CODE" nillable="false">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="3"/>
						<xs:minLength value="0"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MESSAGE" nillable="false">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="256"/>
						<xs:minLength value="0"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:element>
<xs:element name="SESSION_ID" nillable="false">
<xs:simpleType>
<xs:restriction base="xs:string">
<xs:maxLength value="19"/>
<xs:minLength value="0"/>
</xs:restriction>
</xs:simpleType>
</xs:element>
<xs:element name="CITIZEN_NUMBER" nillable="false">
	<xs:simpleType>
		<xs:restriction base="xs:string">
			<xs:maxLength value="13"/>
			<xs:minLength value="0"/>
		</xs:restriction>
	</xs:simpleType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
