<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2010 rel. 3 (http://www.altova.com) by <PERSON><PERSON> (Nadra) -->
<!--Registration Data Transfer Interface	
	Author:  
	Date:  
	Time: 11:00 
	Version: 2.0
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="BIOMETRIC_VERIFICATION" nillable="false">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="USER_VERIFICATION" nillable="false">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="USERNAME" nillable="false">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="12"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="PASSWORD" nillable="false">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="15"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="REQUEST_DATA" nillable="false">
					<xs:complexType>
						<xs:sequence>
						  <xs:element name="TRANSACTION_ID" nillable="false">
							<xs:simpleType>
							  <xs:restriction base="xs:string">
								<xs:maxLength value="19"/>
								<xs:minLength value="0"/>
							  </xs:restriction>
							</xs:simpleType>
						  </xs:element>
						<xs:element name="CITIZEN_NUMBER" nillable="false">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="13"/>
										<xs:minLength value="0"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>											               
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
