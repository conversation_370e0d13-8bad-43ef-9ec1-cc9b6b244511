﻿using Microsoft.EntityFrameworkCore;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.ServiceContracts;
using PLRA.ESORS.ServiceContracts.Features.Court;
namespace PLRA.ESORS.Server.DataServices.Features.Court;
public class CourtServerSideFormDataService : ICourtFormDataService
{

    private readonly ApplicationDbContext _context;
    private readonly IErrorLoggingService _errorLoggingService;

    public CourtServerSideFormDataService(ApplicationDbContext context, IErrorLoggingService errorLoggingService)
    {
        _context = context;
        _errorLoggingService = errorLoggingService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<int> SaveAsync(CourtFormBusinessObject formBusinessObject)
    {
        try
        {
            var existedCourt = await _context.Courts.FirstOrDefaultAsync(x => formBusinessObject.Id == 0 && x.Name_En == formBusinessObject.Name_En);
            if (existedCourt != null) throw new Exception("Record already exists");

            var court = await _context.Courts.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
            if (court == null)
            {
                court = new Data.Entities.Court
                {
                    Id = _context.GetNextValueOfSequence(ApplicationDbContext.CourtSequence),
                    CreatedAt = DateTime.UtcNow,
                };

                _context.Courts.Add(court);
            }

            court.Name_En = formBusinessObject.Name_En;
            court.Name_Ur = formBusinessObject.Name_Ur;
            court.DistrictId = formBusinessObject.DistrictId;
            court.TehsilId = formBusinessObject.TehsilId;
            court.IsActive = formBusinessObject.IsActive;

            var d = await _context.SaveChangesAsync();
            return court.Id;
        }
        catch (Exception ex)
        {
            await _errorLoggingService.LogDatabaseErrorAsync(
                nameof(CourtServerSideFormDataService),
                nameof(SaveAsync),
                ex,
                "Court Save Operation",
                new { CourtData = formBusinessObject }
            );
            throw;
        }
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<CourtFormBusinessObject?> GetItemByIdAsync(int id)
    {
        var court = await _context.Courts.Where(x => x.Id == id).
            Select(x => new CourtFormBusinessObject
            {
                Id = x.Id,
                DistrictId = x.DistrictId,
                IsActive = x.IsActive,
                Name_En = x.Name_En,
                Name_Ur = x.Name_Ur,
                TehsilId = x.TehsilId
                
            }).FirstOrDefaultAsync();

        return court ?? new CourtFormBusinessObject();
    }
}
