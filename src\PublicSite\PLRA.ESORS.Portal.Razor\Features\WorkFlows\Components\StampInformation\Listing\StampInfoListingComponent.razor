﻿@using Microsoft.AspNetCore.Components.Forms
@using PLRA.ESORS.Framework;
@using PLRA.ESORS.Portal.Razor.Components
@using PLRA.ESORS.Portal.Razor.Features.StampInformation;
@using PLRA.ESORS.Portal.ServiceContracts.Features.StampInformation;
@inherits ListingBase<StampInfoListingViewModel,StampInfoListingBusinessObject,StampInfoFilterViewModel,StampInfoFilterBusinessObject, IStampInfoListingDataService>


<div class="border-0 card mb-4 mt-5">
    <div class="bg-white card-header p-0 pb-2">
        <div class="d-flex justify-content-between bg-success text-white align-items-center p-2">
          
            <span class="urdu-text">منتخب رقم کی تقسیم</span>
            <h5 class="mb-0">Selected Denominations</h5>
        </div>
    </div>
    <div class="card-body p-0 pt-3" data-form-listing="@FormName" data-title="Selected Denominations">
        @{

            int index = 0;
        }
        <DataGrid Items="@Items" IsBusy="@IsTableBusy">
            <THead>
                <tr>
                    <th><span class="urdu-text">سیریل نمبر</span></th>
                    <th><span class="urdu-text"> ٹائپ اسٹامپ </span> <span class="float-md-start">Stamp Type</span></th>
                    <th><span class="urdu-text"> اسٹامپ</span> <span class="float-md-start">Stamp</span></th>
                    <th><span class="urdu-text"> رقم کی تقسیم</span> <span class="float-md-start">Denominations</span></th>
                    <th><span class="urdu-text"> اسٹامپ کی تعداد</span> <span class="float-md-start">Number of Stamps</span></th>
                    <th><span class="urdu-text"> رقم</span> <span class="float-md-start">Amount</span></th>
                    <th><span class="urdu-text"> عمل</span> <span class="float-md-start">Action</span></th>
                </tr>
            </THead>
            <TBody>
                @{
                    var item = context;
                }

                <tr>
                    <td>@(++index)</td>
                    <td>@item.StampType</td>
                    <td>@item.StampName</td>
                    <td>@item.Denomination</td>
                    <td>@item.NumberOfStamps</td>
                    <td class="amount">@item.Amount.ToString("N0")</td>
                    <td>
                        <a href="@NavigationManager?.AddQueryString(new { rowId = item.Id})" class="btn btn-warning btn-sm"><i class="fa fa-edit"></i></a>
                        <a href="@NavigationManager?.AddQueryString(new { rowId = item.Id ,  dDialog = "true"})" class="btn btn-danger btn-sm"><i class="fa fa-trash"></i></a>
                    </td>
                 </tr>
            </TBody>
        </DataGrid>
    </div>
</div>


