﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PLRA.ESORS.Server.Data.Data;

#nullable disable

namespace PLRA.ESORS.Server.Data.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250701140001_AddErrorLoggingSystem")]
    partial class AddErrorLoggingSystem
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.HasSequence<int>("AgreementMemorandumSequence", "dbo");

            modelBuilder.HasSequence<int>("AgreementSequence", "dbo");

            modelBuilder.HasSequence<int>("ArticleOfClerkshipSequence", "dbo");

            modelBuilder.HasSequence<int>("AssignedTaskSequence", "dbo");

            modelBuilder.HasSequence<int>("AuthenticatedDeclarationSequence", "dbo");

            modelBuilder.HasSequence<int>("AwardSequence", "dbo");

            modelBuilder.HasSequence<int>("BillofExchange13A2Sequence", "dbo");

            modelBuilder.HasSequence<int>("BillofExchange13B1Sequence", "dbo");

            modelBuilder.HasSequence<int>("BillofExchange13B2Sequence", "dbo");

            modelBuilder.HasSequence<int>("BillofExchange13B3Sequence", "dbo");

            modelBuilder.HasSequence<int>("BillofExchangeSequence", "dbo");

            modelBuilder.HasSequence<int>("BiometricSequence", "dbo");

            modelBuilder.HasSequence<int>("BondSequence", "dbo");

            modelBuilder.HasSequence<int>("BottomryBondSequence", "dbo");

            modelBuilder.HasSequence<int>("BusinessContractSequence", "dbo");

            modelBuilder.HasSequence<int>("CancellationSequence", "dbo");

            modelBuilder.HasSequence<int>("CertificateOfSale18RuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("CertificateOfSale18Sequence", "dbo");

            modelBuilder.HasSequence<int>("Conveyance23RuralAndUrbansSequence", "dbo");

            modelBuilder.HasSequence<int>("Conveyance23Sequence", "dbo");

            modelBuilder.HasSequence<int>("CourtFeeSequence", "dbo");

            modelBuilder.HasSequence<int>("CourtSequence", "dbo");

            modelBuilder.HasSequence<int>("DebentureOrParticipationTermSequence", "dbo");

            modelBuilder.HasSequence<int>("Decree27ARuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Decree27ASequence", "dbo");

            modelBuilder.HasSequence<int>("DeedDepositAgreementSequence", "dbo");

            modelBuilder.HasSequence<int>("Divorce29Sequence", "dbo");

            modelBuilder.HasSequence("ErrorLogSequence", "dbo");

            modelBuilder.HasSequence<int>("EStampMainSequence", "dbo");

            modelBuilder.HasSequence<int>("ExchangeOfImmovableProperty31RuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("ExchangeOfImmovableProperty31Sequence", "dbo");

            modelBuilder.HasSequence<int>("FurtherChargeSequence", "dbo");

            modelBuilder.HasSequence<int>("GeneralPowerOfAttorneyInfoSequence", "dbo");

            modelBuilder.HasSequence<int>("Gift33RuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Gift33Sequence", "dbo");

            modelBuilder.HasSequence<int>("Lease351BRuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Lease351BSequence", "dbo");

            modelBuilder.HasSequence<int>("Lease351C1DRuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Lease351C1DSequence", "dbo");

            modelBuilder.HasSequence<int>("Lease352ARuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Lease352ASequence", "dbo");

            modelBuilder.HasSequence<int>("LeaseSequence", "dbo");

            modelBuilder.HasSequence<int>("LeaseTransfer63RuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("LeaseTransfer63Sequence", "dbo");

            modelBuilder.HasSequence<int>("Memorandum5ccRuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Memorandum5ccSequence", "dbo");

            modelBuilder.HasSequence<int>("MortgageForHousingFinanceivSequence", "dbo");

            modelBuilder.HasSequence<int>("MortgageSequence", "dbo");

            modelBuilder.HasSequence<int>("NoteOrMemorandumSequence", "dbo");

            modelBuilder.HasSequence<int>("PartiesSequence", "dbo");

            modelBuilder.HasSequence<int>("PartitionSequence", "dbo");

            modelBuilder.HasSequence<int>("PartnershipSequence", "dbo");

            modelBuilder.HasSequence<int>("PaymentDetailSequence", "dbo");

            modelBuilder.HasSequence<int>("PaymentSequence", "dbo");

            modelBuilder.HasSequence<int>("POA48bRuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("POA48bSequence", "dbo");

            modelBuilder.HasSequence<int>("PromissoryNoteSequence", "dbo");

            modelBuilder.HasSequence<int>("PropertyDetailSequence", "dbo");

            modelBuilder.HasSequence<int>("PropertyPowerOfAttorneyInfoSequence", "dbo");

            modelBuilder.HasSequence<int>("PurposeSequence", "dbo");

            modelBuilder.HasSequence<int>("Rectification159Sequence", "dbo");

            modelBuilder.HasSequence<int>("Release55ARuralAndUrbanSequence", "dbo");

            modelBuilder.HasSequence<int>("Release55ASequence", "dbo");

            modelBuilder.HasSequence<int>("ResourceSequence", "dbo");

            modelBuilder.HasSequence<int>("RespondentiaBond56Sequence", "dbo");

            modelBuilder.HasSequence<int>("SettlementSequence", "dbo");

            modelBuilder.HasSequence<int>("ShareWarrent59Sequence", "dbo");

            modelBuilder.HasSequence<int>("StampInfoSequence", "dbo");

            modelBuilder.HasSequence<int>("SurrenderOfLeaseSequence", "dbo");

            modelBuilder.HasSequence<int>("Transfer62bSequence", "dbo");

            modelBuilder.HasSequence<int>("TransferSequence", "dbo");

            modelBuilder.HasSequence<int>("WorkFlowExecutionLogSequence", "dbo");

            modelBuilder.HasSequence<int>("WorkFlowSequence", "dbo");

            modelBuilder.HasSequence<int>("WorkFlowStepSequence", "dbo");

            modelBuilder.HasSequence<int>("WorkFlowVerificationStepSequence", "dbo");

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Data.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<int?>("EmailOTP")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeactivated")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<int?>("MobileOTP")
                        .HasColumnType("int");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Agreement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Data")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.ToTable("Agreements");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AgreementMemorandum", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AgreementDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("AgreementPurpose")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AgreementType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CollectionArea")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal?>("Commission")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Duration")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("FeeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("MemorandumType")
                        .HasColumnType("int");

                    b.Property<string>("ModeOfPayment")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NatureOfTheAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NumberOfAgreement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PaymentSchedule")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PerSharePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TotalNumberOfShares")
                        .HasColumnType("int");

                    b.Property<string>("TypeOfTax")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("AgreementMemorandums");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AgreementMemorandum5CC", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("AdvanceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BalanceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("FinalPaymentDueDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalSalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("AgreementMemorandum5CCs");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AgreementMemorandum5CCRuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgreementMemorandum5CCId")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Memorandum5CCRuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ArticleOfClerkship", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DurationOfClerkship")
                        .HasColumnType("int");

                    b.Property<string>("DurationOfTraining")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Duties")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("FeeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InstitutionName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("WorkingHours")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("ArticleOfClerkships");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AssignedTasks", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("SubmittedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("SubmittedTo")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.ToTable("AssignedTasks");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AuthenticatedDeclaration", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AuthorityName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("ConsiderationAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DeclarationDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfTheAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("VerifiedOn")
                        .HasMaxLength(100)
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("AuthenticatedDeclarations");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Award", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AwardDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("CompensationAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("ComplianceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisputeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Awards");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BillOfExchanges");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13A2", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("NumberOfCopiesIssued")
                        .HasColumnType("int");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("StampDutyPerCopy")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BillOfExchanges13A2");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13B1", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("IssueDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("NumberOfCopiesIssued")
                        .HasColumnType("int");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BillOfExchanges13B1");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13B2", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("ChargesPerPart")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("NumberOfPartsIssued")
                        .HasColumnType("int");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BillOfExchanges13B2");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13B3", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DutyPerCopy")
                        .HasColumnType("int");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("NumberOfCopiesIssued")
                        .HasColumnType("int");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BillOfExchanges13B3");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BiometricVerification", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("PartyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<byte>("VERIFICATION_TYPE")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.ToTable("BiometricVerifications");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Bond", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AgreementDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("BondAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("MaturityDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Bonds");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BottomryBond", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AgreementDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("BondAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Destination")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Duration")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("InterestRate")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfTheAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Registration")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ShipName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Tonnage")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BottomryBonds");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BusinessContract", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<byte>("CONTRACT_TYPE")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Duration")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("From")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("Scope")
                        .HasColumnType("int");

                    b.Property<string>("Terms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("To")
                        .HasColumnType("datetime2");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("BusinessContracts");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Cancellation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AgreementDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CancellationDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("ConsiderationAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Cancellations");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.CertificateOfSale18", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("AdvanceMoney")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("AuctionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("AuctionRefNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BidEndAmount")
                        .HasColumnType("int");

                    b.Property<int?>("BidStartAmount")
                        .HasColumnType("int");

                    b.Property<string>("CertificateNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("DateOfIssuance")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("RegisterStamp")
                        .HasColumnType("bit");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("CertificateOfSale18s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.CertificateOfSale18RuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CertificateOfSaleId")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("CertificateOfSale18RuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Conveyance23", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BuildingPlanAttached")
                        .HasColumnType("bit");

                    b.Property<short>("CONVEYANCE_TYPE")
                        .HasColumnType("smallint");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("RegisterStamp")
                        .HasColumnType("bit");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("Transfer_Direction")
                        .HasColumnType("smallint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Conveyance23s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Conveyance23RuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Conveyance23Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Conveyance23RuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Court", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name_En")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("Name_Ur")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("TehsilId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.ToTable("Courts");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.CourtFee", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CaseNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CaseTitle")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CourtId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("FeeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("JudgeName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("CourtFees");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.DebentureOrParticipationTerm", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CapitalRaised")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("InstrumentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<float>("InterestRate")
                        .HasColumnType("real");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("MaturityPeriod")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("DebentureOrParticipationTerms");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Decree27A", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<string>("CourtName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("DecreeDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DecreeReferenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Decree27As");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Decree27ARuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("Decree27AId")
                        .HasColumnType("bigint");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Decree27ARuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.DeedDepositAgreement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Duration")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal>("LoanAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MarkUpRate")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<bool>("RegisterStamp")
                        .HasColumnType("bit");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("DeedDepositAgreements");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Divorce29", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("FeeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("Title")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Divorce29s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.EStampMain", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("MauzaId")
                        .HasColumnType("int");

                    b.Property<int>("PurposeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StampDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("TehsilId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("WorkflowId")
                        .HasColumnType("int");

                    b.Property<int>("WorkflowStatus")
                        .HasColumnType("int");

                    b.Property<int>("WorkflowStepId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PurposeId");

                    b.HasIndex("WorkflowId");

                    b.ToTable("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ErrorLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AdditionalData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComponentName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ErrorCategory")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("InnerException")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsResolved")
                        .HasColumnType("bit");

                    b.Property<string>("MethodName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RequestPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ResolutionNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ResolvedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SeverityLevel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("StackTrace")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("StatusCode")
                        .HasColumnType("int");

                    b.Property<long?>("TaskId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("UserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<long?>("WorkflowId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("ErrorLogs");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ExchangeOfImmovableProperty31", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PartyType")
                        .HasColumnType("int");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("ExchangeOfImmovableProperty31s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ExchangeOfImmovableProperty31RuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<long>("ExchangeOfPropertyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("ExchangeOfImmovableProperty31RuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.FurtherCharge", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FurtherChargeType")
                        .HasColumnType("int");

                    b.Property<int>("FurtherLoanAmount")
                        .HasColumnType("int");

                    b.Property<int>("InitialLoanAmount")
                        .HasColumnType("int");

                    b.Property<float>("InterestRate")
                        .HasColumnType("real");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("MainMortgageRef")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("MortgageDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MortgagedProperty")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime>("RepaymentTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TypeOfMortgage")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("FurtherCharges");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.GeneralPowerOfAttorneyInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("AgreementDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("DocumentPurpose")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("DocumentTitle")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("FeeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("GeneralPowerOfAttorneyInfo");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Gift33", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsChallanPurpose")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGift")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHousingSocietyIncluded")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PurposeOfChallan")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Gift33s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Gift33RuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Gift33Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Gift33RuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<int?>("Duration")
                        .HasColumnType("int");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Measurement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("MonthlyRent")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PaymentMode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SecurityDeposit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("TehsilId")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Leases");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351B", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AnnualLeaseAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("BuildingPlanAttached")
                        .HasColumnType("bit");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("LeasePeriod")
                        .HasColumnType("int");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Lease351Bs");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351BRuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Lease351BId")
                        .HasColumnType("bigint");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Lease351BRuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351C1D", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LEASE_TYPE")
                        .HasColumnType("int");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("LeasePeriod")
                        .HasColumnType("int");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Lease351C1Ds");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351C1DRuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Lease351C1DId")
                        .HasColumnType("bigint");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Lease351C1DRuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease352A", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AverageAnnualRent")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LEASE_TYPE")
                        .HasColumnType("int");

                    b.Property<decimal?>("LandValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Lease352As");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease352ARuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Lease352AId")
                        .HasColumnType("bigint");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Lease352ARuralAndUrban");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.LeaseTransfer63", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BuildingPlanAttached")
                        .HasColumnType("bit");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("LeaseTransfer63s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.LeaseTransfer63RuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("LeaseTransfer63Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("LeaseTransfer63RuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Mortgage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Area")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("CropType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("ExpectedYield")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("FeeAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InterestRate")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal>("LoanAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("LoanReturnDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Location")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("MortgageDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MortgageDeedNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MortgageType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NatureOfTheAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PlotNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PossessionDetails")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PropertyDetails")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PropertyType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReturnPeriod")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Mortgages");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.MortgageForHousingFinanceiv", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<int>("MarkupRate")
                        .HasColumnType("int");

                    b.Property<DateTime>("MortgageDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfTheAgreement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("NewLoanAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OldLoanAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RepaymentTime")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TypeOfMortgage")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("MortgageForHousingFinanceivs");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.NoteOrMemorandum", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Advance")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("PriceperUnit")
                        .HasColumnType("int");

                    b.Property<string>("SubjectofMemorandum")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("NoteOrMemorandums");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Parties", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<long?>("CNIC")
                        .HasMaxLength(13)
                        .HasColumnType("bigint");

                    b.Property<int>("CompanyType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Email")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LawyerName")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<long?>("MobileNo")
                        .HasMaxLength(12)
                        .HasColumnType("bigint");

                    b.Property<string>("NTN")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("Name")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("Party_Type")
                        .HasColumnType("int");

                    b.Property<long?>("PhoneNumber")
                        .HasMaxLength(12)
                        .HasColumnType("bigint");

                    b.Property<string>("RegistrationNo")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Relation")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RelativeName")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<long?>("RepresentativeCNIC")
                        .HasMaxLength(13)
                        .HasColumnType("bigint");

                    b.Property<string>("RepresentativeDesignation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RepresentativeName")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Parties");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Partition", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CompensationAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ExecutionDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("KhasraNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("PropertyType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Propertylocation")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TotalArea")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Partitions");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Partnership", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BankDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("CapitalContribution")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<int>("Duration")
                        .HasColumnType("int");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProfitSharing")
                        .HasColumnType("int");

                    b.Property<string>("ScopeofBusiness")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("TehsilId")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Partnerships");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Payment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("JsonData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PaymentDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("APIResponse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BankCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("ChallanNo")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FEE_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("GatewayPSIDStatus")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("GatewayPaidDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("PAYMENT_STATUS")
                        .HasColumnType("int");

                    b.Property<string>("PSID")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("PartyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("PartyCNIC")
                        .HasColumnType("bigint");

                    b.Property<string>("PartyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PaymentDueDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("PaymentId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("PaymentDetails");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PowerOfAttorney48b", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("PowerOfAttorney48bs");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PowerOfAttorney48bRuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("POA48bId")
                        .HasColumnType("bigint");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("PowerOfAttorney48bRuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PromissoryNote", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("BondAmount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DateOfPayment")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("PromissoryNoteType")
                        .HasColumnType("int");

                    b.Property<string>("Purpose")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("PromissoryNotes");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PropertyDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AddressLine")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("AreaOfProperty")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CityTown")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistryFardNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TehsilId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("PropertyDetails");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PropertyPowerOfAttorneyInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime>("AgreementDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("DocumentPurpose")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("DocumentTitle")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("FeeAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("PlotNo")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("PropertyType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("PropertyPowerOfAttorneyInfo");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Purpose", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description_En")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description_Ur")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IconPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name_En")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name_Ur")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<byte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int?>("WorkflowId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name_En");

                    b.ToTable("Purposes");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Rectification159", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("ConsiderationAmount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("RegistrarOffice")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("RegistrationNumber")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TypeOfAgreement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Rectification159s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Release55A", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BuildingPlanAttached")
                        .HasColumnType("bit");

                    b.Property<int?>("ConstructedArea")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructureDCRate")
                        .HasColumnType("int");

                    b.Property<int>("ConstructedStructurePrice")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("DCLandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DCPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("FARD_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("FardData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LandValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PropertyValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RecordId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SUBMISSION_TO")
                        .HasColumnType("int");

                    b.Property<decimal?>("StructureValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Release55As");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Release55ARuralAndUrban", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<string>("Floor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("KhasraNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhatoniNo")
                        .HasColumnType("bigint");

                    b.Property<long>("KhewatNo")
                        .HasColumnType("bigint");

                    b.Property<string>("LandClassification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mauza")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnedArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PROPERTY_TYPE")
                        .HasColumnType("int");

                    b.Property<string>("PropertyArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QanoonGoi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Release55AId")
                        .HasColumnType("bigint");

                    b.Property<string>("RevenueCircle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferredShare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Release55ARuralAndUrbans");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Resource", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("FileSize")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("RecordId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResourceName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ResourcePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Resources");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.RespondentiaBond56", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("EstimatedArrival")
                        .HasColumnType("datetime2");

                    b.Property<string>("Goods")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal>("LoanAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MarkupRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NatureOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShipName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShippingFrom")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShippingTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TitleOfAgreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("RespondentiaBond56s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Settlement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRegistrationFee")
                        .HasColumnType("bit");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<short>("SETTLEMENT_TYPE")
                        .HasColumnType("smallint");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Settlements");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ShareWarrent59", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("ConsiderationAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("IssueDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("NumberOfShares")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("Type")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("WarrantNumber")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("ShareWarrent59s");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.StampInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Denomination")
                        .HasColumnType("int");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<int>("NumberOfStamps")
                        .HasColumnType("int");

                    b.Property<int>("StampId")
                        .HasColumnType("int");

                    b.Property<int>("StampTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("StampInfo");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.SurrenderOfLease", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("AnnualRent")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LeaseEndDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LeasePeriod")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LeaseStartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int?>("SecurityDeposit")
                        .HasColumnType("int");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("SurrenderOfLeases");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Transfer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BankName")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("BranchCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CertificateNo")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ConsiderationAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DateOfExecution")
                        .HasColumnType("datetime2");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("FaceValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("IssueDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MaturityDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("NumberOfShares")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal?>("PricePerShare")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TitleOfAgreement")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal?>("TotalAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TypeOfAgreement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Transfers");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Transfer62b", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CertificateNo")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<decimal?>("ConsiderationAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<long>("EStampMainId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("FaceValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InstrumentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("IssueDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("MaturityDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NatureOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("TitleOfAgreement")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("TransferDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EStampMainId");

                    b.ToTable("Transfer62bs");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.UserProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("CurrentAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("PermanentAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("Id");

                    b.ToTable("Profiles");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkFlow", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("RequiresVerification")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.ToTable("WorkFlows");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkFlowStep", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Attributes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComponentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<int>("WorkFlowId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("WorkFlowId");

                    b.ToTable("WorkFlowSteps");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkFlowVerificationStep", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("ComponentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Role")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<int>("WorkFlowId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("WorkFlowId");

                    b.ToTable("WorkFlowVerificationSteps");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkflowExecutionLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Data")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("WorkflowStepId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("WorkflowStepId");

                    b.ToTable("WorkflowExecutionLogs");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PLRA.ESORS.Server.Data.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AgreementMemorandum", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AgreementMemorandum5CC", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AgreementMemorandum5CCRuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ArticleOfClerkship", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.AuthenticatedDeclaration", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Award", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13A2", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13B1", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13B2", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BillOfExchange13B3", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Bond", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BottomryBond", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.BusinessContract", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Cancellation", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.CertificateOfSale18", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.CertificateOfSale18RuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Conveyance23", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Conveyance23RuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.CourtFee", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.DebentureOrParticipationTerm", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Decree27A", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Decree27ARuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.DeedDepositAgreement", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Divorce29", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.EStampMain", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.Purpose", "Purpose")
                        .WithMany()
                        .HasForeignKey("PurposeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PLRA.ESORS.Server.Data.Entities.WorkFlow", "WorkFlow")
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Purpose");

                    b.Navigation("WorkFlow");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ExchangeOfImmovableProperty31", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ExchangeOfImmovableProperty31RuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.FurtherCharge", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.GeneralPowerOfAttorneyInfo", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Gift33", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Gift33RuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351B", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351BRuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351C1D", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease351C1DRuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease352A", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Lease352ARuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.LeaseTransfer63", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.LeaseTransfer63RuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Mortgage", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.MortgageForHousingFinanceiv", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.NoteOrMemorandum", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Parties", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Partition", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Partnership", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Payment", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PaymentDetail", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PowerOfAttorney48b", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PowerOfAttorney48bRuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PromissoryNote", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PropertyDetail", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.PropertyPowerOfAttorneyInfo", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Rectification159", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Release55A", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Release55ARuralAndUrban", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Resource", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.RespondentiaBond56", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Settlement", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.ShareWarrent59", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.StampInfo", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.SurrenderOfLease", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Transfer", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.Transfer62b", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.EStampMain", "EStampMain")
                        .WithMany()
                        .HasForeignKey("EStampMainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EStampMain");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.UserProfile", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Data.ApplicationUser", "ApplicationUser")
                        .WithOne("Profile")
                        .HasForeignKey("PLRA.ESORS.Server.Data.Entities.UserProfile", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApplicationUser");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkFlowStep", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.WorkFlow", "WorkFlow")
                        .WithMany()
                        .HasForeignKey("WorkFlowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkFlow");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkFlowVerificationStep", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.WorkFlow", "WorkFlow")
                        .WithMany()
                        .HasForeignKey("WorkFlowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkFlow");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Entities.WorkflowExecutionLog", b =>
                {
                    b.HasOne("PLRA.ESORS.Server.Data.Entities.WorkFlowStep", "WorkflowStep")
                        .WithMany()
                        .HasForeignKey("WorkflowStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkflowStep");
                });

            modelBuilder.Entity("PLRA.ESORS.Server.Data.Data.ApplicationUser", b =>
                {
                    b.Navigation("Profile")
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
