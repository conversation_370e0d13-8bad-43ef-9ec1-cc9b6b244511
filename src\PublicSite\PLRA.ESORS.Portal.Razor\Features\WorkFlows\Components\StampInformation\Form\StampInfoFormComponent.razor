﻿@using PLRA.ESORS.Framework;
@using PLRA.ESORS.Portal.Razor.Components
@using PLRA.ESORS.Portal.Razor.Features.StampInformation;
@using PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.StampInformation.Listing
@using PLRA.ESORS.Portal.ServiceContracts.Features.StampInformation;
@using Microsoft.AspNetCore.Components.Forms
@inherits FormBase<StampInfoFormBusinessObject,StampInfoFormViewModel, long, IStampInfoFormDataService>

<div class="border-0 card mb-4 p-5 rounded-4 shadow-sm">

    <div class="bg-white card-header p-0 pb-2">
        <div class="d-flex justify-content-between align-items-center">

            <h5 class="mb-0">Stamp Information</h5>
            <span class="urdu-text">اسٹامپ کی تفصیل</span>


        </div>
    </div>
    <div class="card-body" dir="rtl">
        @if (SelectedItem != null)
        {
            <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnValidSubmit" FormName="StampInfo">
                <DataAnnotationsValidator></DataAnnotationsValidator>


                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between">
                            
                            <label for="stamptype" class="form-label urdu-text">اسٹامپ ٹائپ</label>
                            <label for="stamptype" class="form-label">Stamp Type</label>
                        </div>
                        <InputSelect id="stampType" class="form-control" @bind-Value="SelectedItem.StampTypeId">
                            @foreach (var stamp in StampTypes)
                            {
                                <option value="@stamp.Value">@stamp.Text</option>
                            }
                        </InputSelect>
                        <ValidationMessage For="() => SelectedItem.StampTypeId" class="text-danger" />
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between">
                            <label for="stamp" class="form-label urdu-text">اسٹامپ </label>
                            <label for="stamp" class="form-label">Stamp </label>
                           
                        </div>
                        <InputSelect id="stamp" class="form-control" @bind-Value="SelectedItem.StampId">
                            @foreach (var stamp in Stamps)
                            {
                                <option value="@stamp.Value">@stamp.Text</option>
                            }
                        </InputSelect>
                        <ValidationMessage For="() => SelectedItem.StampId" class="text-danger" />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="d-flex justify-content-between">
                            <label for="denominations" class="form-label urdu-text">رقم کی تقسیم</label>
                            <label for="denominations" class="form-label">Denominations</label>
                            
                        </div>
                        <InputSelect TValue="int?" id="denominations" class="form-control urdu-text" @bind-Value="SelectedItem.Denomination" field-required>
                            <option value="">ڈاک کے ٹکٹوں میں مالیت کا انتخاب کریں</option>
                            <option value="2">2</option>
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="25">25</option>
                            <option value="30">30</option>
                            <option value="40">40</option>
                            <option value="50">50</option>
                            <option value="200">200</option>
                        </InputSelect>

                        <ValidationMessage For="() => SelectedItem.Denomination" class="text-danger" />
                    </div>
                    
                    <div class="col-md-4">
                        <div class="d-flex justify-content-between">
                            <label for="nostamps" class="form-label urdu-text">اسٹامپ کی تعداد</label>
                            <label for="nostamps" class="form-label">Number of Stamps</label>
                            
                        </div>
                        <InputNumber id="noofstamops" class="form-control" @bind-Value="SelectedItem.NumberOfStamps" field-required />
                        <ValidationMessage For="() => SelectedItem.NumberOfStamps" class="text-danger" />
                    </div>

                    <div class="col-md-4">
                        <div class="d-flex justify-content-between">
                            <label for="amount" class="form-label urdu-text">رقم</label>
                            <label for="amount" class="form-label">Amount</label>
                           
                        </div>
                        <InputNumber id="amount" class="form-control" readonly @bind-Value="SelectedItem.Amount" field-required />
                        <ValidationMessage For="() => SelectedItem.Amount" class="text-danger" />
                    </div>
                </div>

                <input type="hidden" id="hdn-adhesiveStamp" name="SelectedItem.Id" value="@SelectedItem.Id" />
                <div class="row">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-success urdu-text" id="StampSubmit">شامل کریں</button>
                    </div>
                </div>





            </EditForm>
        }

        <StampInfoListingComponent TaskId="@TaskId" Step="@Step" FormName="StampInfo"></StampInfoListingComponent>
    </div>
</div>

