
public static void VerifyFingerPrints()
        {
            try
            {			
				//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
				//////////////////////////////////////////////// Verify Fingerprints Example  ////////////////////////////////////////////////////////////////////////					
				//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////			
                BioVeriSys bvs = new BioVeriSys();
                Random random = new Random();

                string filename = @"E:\TestingData\ZabiUllah WSQ\ISO_1.tpl";
                byte[] bmpbyteArray = System.IO.File.ReadAllBytes(filename);

                string newCode = "1009" + random.Next(1000000, 9999999).ToString("0000000") + random.Next(10000000, 99999999).ToString("00000000");

                string xmlSource = @"<BIOMETRIC_VERIFICATION><USER_VERIFICATION><USERNAME>bioverisys</USERNAME><PASSWORD>pak.058</PASSWORD></USER_VERIFICATION> " +
                                   " <REQUEST_DATA> " +
                                       " <TRANSACTION_ID>" + newCode + "</TRANSACTION_ID> " +
                                       " <SESSION_ID></SESSION_ID> " +
                                       " <CITIZEN_NUMBER>3740560420093</CITIZEN_NUMBER> " +
                                       " <CONTACT_NUMBER></CONTACT_NUMBER> " +
                                       " <FINGER_INDEX>1</FINGER_INDEX> " +
                                       " <FINGER_TEMPLATE>" + Convert.ToBase64String(bmpbyteArray) + "</FINGER_TEMPLATE> " +
                                       " <TEMPLATE_TYPE>" + TemplateType.SAGEM_PKMAT + "</TEMPLATE_TYPE> " +
                                       " <AREA_NAME>PUNJAB</AREA_NAME> " +
                                       " </REQUEST_DATA> " +
                                   " </BIOMETRIC_VERIFICATION>";

                string response = bvs.VerifyFingerPrints("1009", xmlSource);
                Console.WriteLine(response);
				
				
				//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
				//////////////////////////////////////////////// Get Last Verification Example////////////////////////////////////////////////////////////////////////					
				//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////				
				string xmlSource = @"<BIOMETRIC_VERIFICATION><USER_VERIFICATION><USERNAME>bioverisys</USERNAME><PASSWORD>pak.058</PASSWORD></USER_VERIFICATION>" +
                                         "<REQUEST_DATA> " +
											"<CITIZEN_NUMBER>3740560420093</CITIZEN_NUMBER> " +
											"<TRANSACTION_ID>1001747744375645811</TRANSACTION_ID>  " +
                                         "</REQUEST_DATA> " +
									"</BIOMETRIC_VERIFICATION>";

				string response = bvs.GetLastVerificationResults("1009", xmlSource);
                Console.WriteLine(response);
				
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }
                