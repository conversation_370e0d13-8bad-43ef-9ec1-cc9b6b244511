﻿class eSoRSEvents {
    constructor() {
        this.cameraStream = null;
        this.loader = document.getElementById('loader');
        this.init();
        this.overrideFetch();
        this.bindEnhancedLoadEvent();
    }

    // Initialize functions for both normal and Blazor navigation
    init() {
        this.SignUpRadios();
        this.ShowPopoverAndToolTips();
        this.ToggleProfileDorpdownMenu();
        this.ToggleSidebar();
        this.SearchPurposeAndSubPurpose();
        this.SearchAppealentCNIC();
        this.SearchAttornyCNIC();
        this.SearchRespondantCNIC();
        this.BasicCourtFee();
        this.Conveyance23BasicInfo();
        this.BiometricVerifications();
        this.DashboardFilters();
        this.PowerOfAttorny();
        this.StepperFormSubmissions("stepper-container");
        this.PrintPreview();
        this.InitializeCamera();
        this.InitializeBiometric();
        this.DropdownsSelectionChanged();
        this.UploadFile();
        this.CalculateStampDenominationAmount();
    }

    // Override fetch to handle Blazor enhanced navigation
    overrideFetch() {
        const { fetch: originalFetch } = window;
        window.fetch = async (...args) => {
            const [url, options] = args;
            if (options && options.headers && options.headers['accept']?.includes('blazor-enhanced-nav')) {
                this.showLoader(true);
                console.log('Show Loader');
            }
            return await originalFetch(...args);
        };
    }

    // Bind enhanced load event for Blazor-specific navigation
    bindEnhancedLoadEvent() {
        Blazor.addEventListener('enhancedload', () => {
            this.init();
            console.log('enhancedload event fired');
            this.showLoader(false);
        });
    }

    showLoader(isLoading) {
        if (isLoading) {
            // this.preloader.style.display = 'none'; // Hide preloader
            this.loader.style.display = 'block';  // Show loader
        } else {
            //this.preloader.style.display = 'none'; // Ensure preloader is hidden
            this.loader.style.display = 'none';   // Hide loader
        }
    }

    // Radios on Signup screen
    SignUpRadios() {
        $(document).on('click', '.image-option', function () {
            // Remove classes from all labels first
            $('.image-option').removeClass('border-success border-3');

            // Then check if the radio is checked (it will be after click)
            let radio = $(this).find('input[type=radio]');
            if (radio.prop('checked') || radio.is(':checked')) {
                $(this).addClass('border-success border-3');
            }
        });
    }

    ShowPopoverAndToolTips() {
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((tooltip) => {
            new bootstrap.Tooltip(tooltip);
        });
        document.querySelectorAll('[data-bs-toggle="popover"]').forEach((popover) => {
            new bootstrap.Popover(popover);
        });
    }

    ToggleProfileDorpdownMenu() {
        // Toggle dropdown menu when clicking on profile
        var element = document.getElementById('profileDropdown');
        if (element != null) {
            element.addEventListener('click', function (e) {
                document.getElementById('profileMenu').classList.toggle('show');
                e.stopPropagation();
            });
        }


        // Close the dropdown when clicking outside of it
        document.addEventListener('click', function (e) {
            const dropdown = document.getElementById('profileMenu');
            if (dropdown != null && dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            }

        });
    }

    ToggleSidebar() {
        // Get elements
        const hamburger = document.getElementById('hamburger-toggle');
        const sidebar = document.getElementById('sidebar');
        if (sidebar != null && hamburger != null) {
            // Add click event listener to hamburger icon
            hamburger.addEventListener('click', function () {
                // Toggle the 'hidden' class on the sidebar
                sidebar.classList.toggle('d-none');
            });

            // For responsive design - detect window size
            function checkWindowSize() {
                if (sidebar) {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('d-none');
                    }
                }
            }

            // Initial check when page loads
            window.addEventListener('load', checkWindowSize);

            // Check again if window is resized
            window.addEventListener('resize', checkWindowSize);
        }

    }

    SearchPurposeAndSubPurpose() {
        var btnSearch = document.getElementById("btnSearch");
        $("#txtPurpose").on('change keydown paste input', function () {
            if ($("#txtPurpose").val() != '') {
                btnSearch.href = "/purpose/" + $(this).val().toLowerCase().replace(/\s+/g, "-").replace(/[^a-z0-9\-]/g, "");
            }
            else {
                btnSearch.href = "/purpose";
            }
        });


        var btnSubSearch = document.getElementById("btnSubSearch");
        $("#txtSubPurpose").on('change keydown paste input', function () {
            if ($("#txtSubPurpose").val() != '') {
                btnSubSearch.href = "/purpose/" + $("#txtParentPurpose").val().toLowerCase().replace(/\s+/g, "-").replace(/[^a-z0-9\-]/g, "") + "?query=" + $("#txtSubPurpose").val().toLowerCase().replace(/\s+/g, "-").replace(/[^a-z0-9\-]/g, "");
            }
            else {
                btnSubSearch.href = "/purpose/" + $("#txtParentPurpose").val().toLowerCase().replace(/\s+/g, "-").replace(/[^a-z0-9\-]/g, "");
            }
        });
    }

    SearchAppealentCNIC() {
        var btnCNICSearch = document.getElementById("btn-Appealent_CNIC-Search");
        if (btnCNICSearch) {
            btnCNICSearch.addEventListener('click', async function () {
                const url = new URL(window.location.href);
                var txtCnic = document.getElementById("txt_Appealent_CNIC");
                var party = txtCnic.getAttribute("data-party");
                url.searchParams.set("cnic", txtCnic.value);
                url.searchParams.set("partyType", party);
                window.location.href = url.toString();
            });

        }
    }

    SearchAttornyCNIC() {
        var btnCNICSearch = document.getElementById("btn-Attorney_CNIC-Search");
        if (btnCNICSearch) {
            btnCNICSearch.addEventListener('click', async function () {
                const url = new URL(window.location.href);
                var txtCnic = document.getElementById("txt_Attorney_CNIC");
                var party = txtCnic.getAttribute("data-party");
                url.searchParams.set("cnic", txtCnic.value);
                url.searchParams.set("partyType", party);
                window.location.href = url.toString();
            });

        }
    }

    SearchRespondantCNIC() {
        var btnCNICSearch = document.getElementById("btn-Respondent_CNIC-Search");
        if (btnCNICSearch) {
            btnCNICSearch.addEventListener('click', async function () {
                const url = new URL(window.location.href);
                var txtCnic = document.getElementById("txt_Respondent_CNIC");
                var party = txtCnic.getAttribute("data-party");
                url.searchParams.set("cnic", txtCnic.value);
                url.searchParams.set("partyType", party);
                window.location.href = url.toString();
            });

        }
    }

    BasicCourtFee() {

        const dutyAmountInput = document.getElementById('dutyAmount');
        if (dutyAmountInput) {
            dutyAmountInput.addEventListener('input', function () {
                const value = this.value;

                const form = document.getElementById('BasicCourtInfoForm');
                if (form) {
                    const min = parseFloat(form.dataset.min);
                    const max = parseFloat(form.dataset.max);

                    if (value < min || value > max) {
                        const message = value < min
                            ? `Minimum amount required is ${min}`
                            : `Maximum amount allowed is ${max}`;

                        ShowToast({
                            title: 'Error',
                            message,
                            status: 'error'
                        });
                        return;
                    }
                }
                updatePaymentDetails(value);
            });
        }

        function updatePaymentDetails(value) {
            // Clean the input value (remove non-numeric characters except decimal point)
            const numericValue = value.replace(/[^\d.]/g, '');
            const amount = parseFloat(numericValue) || 0;

            // Format the amount with thousand separators
            const formatter = new Intl.NumberFormat('en-US', {
                maximumFractionDigits: 1,
                minimumFractionDigits: 0
            });

            const formattedAmount = formatter.format(amount);

            // Update the display values
            document.getElementById('baseAmount').textContent = formattedAmount + ' PKR';
            document.getElementById('totalAmount').textContent = formattedAmount + ' PKR';
        }

    }

    Conveyance23BasicInfo() {
        const inputFard = document.getElementById("fardId");
        const btnSearchFard = document.getElementById("btnSearchFard");
        const landValue = document.getElementById('land-value');
        const constructedArea = document.getElementById('constructedArea');

        const radioGroup = document.getElementById("fard-radio-group");
        const manualFardradioGroup = document.getElementById("manual-fard-radio-group");
        const agreementSubmissionRadioGroup = document.getElementById("agreement-submission-radio-group");

        const params = new URLSearchParams(window.location.search);
        const fardtype = params.get("fardtype");
        const propertytype = params.get("propertytype");
        const fardId = params.get("fardId");


        if (landValue) {
            landValue.addEventListener('input', function () {
                const value = this.value;
                updatePaymentDetails(value, 'person-land-value');
            });
        }

        if (constructedArea) {
            constructedArea.addEventListener('input', function () {
                const value = this.value;
                CalculateConstructedAreaDCPrice(value);
            });
        }

        // Pre-select the radio based on the URL
        if (fardtype) {
            const radioToCheck = document.querySelector(`input[name="SelectedItem.FARD_TYPE"][value="${fardtype}"]`);
            if (radioToCheck) {
                radioToCheck.checked = true;
            }
        }

        // Pre-select the radio based on the URL
        if (propertytype) {
            const radioToCheck = document.querySelector(`input[name="PROPERTY_TYPE"][value="${propertytype}"]`);
            if (radioToCheck) {
                radioToCheck.checked = true;
            }
        }

        if (fardId && inputFard) {
            inputFard.value = fardId;
        }

        if (radioGroup) {
            radioGroup.addEventListener("change", function (e) {
                if (e.target && e.target.name === "SelectedFard") {
                    const selectedValue = e.target.value;

                    const url = new URL(window.location.href);
                    url.searchParams.set("fardtype", selectedValue);

                    window.location.href = url.toString();
                }
            });
        }

        if (manualFardradioGroup) {
            manualFardradioGroup.addEventListener("change", function (e) {
                if (e.target && e.target.name === "PROPERTY_TYPE") {
                    const selectedValue = e.target.value;

                    const url = new URL(window.location.href);
                    url.searchParams.set("propertytype", selectedValue);

                    window.location.href = url.toString();
                }
            });
        }

        if (btnSearchFard) {
            btnSearchFard.addEventListener("click", function () {
                if (inputFard.value != null) {
                    const url = new URL(window.location.href);
                    url.searchParams.set("fardId", inputFard.value);

                    window.location.href = url.toString();
                }
            });
        }

        function CalculateConstructedAreaDCPrice(value) {
            // Clean the input value (remove non-numeric characters except decimal point)
            const numericValue = value.replace(/[^\d.]/g, '');
            const amount = parseFloat(numericValue) || 0;

            // Format the amount with thousand separators
            const formatter = new Intl.NumberFormat('en-US', {
                maximumFractionDigits: 1,
                minimumFractionDigits: 0
            });

            const hdnConstructedStructureDCRate = document.getElementById('hdnConstructedStructureDCRate');
            // Update the display values
            const element = document.getElementById('constructed-structure-dcprice');
            var calculatePrice = hdnConstructedStructureDCRate.value * amount;
            element.textContent = formatter.format(calculatePrice) + ' PKR';
            const hdnelement = document.getElementById('hdnConstructedStructureCalculatedPrice');
            hdnelement.value = calculatePrice;
        }

        function updatePaymentDetails(value, classname) {
            // Clean the input value (remove non-numeric characters except decimal point)
            const numericValue = value.replace(/[^\d.]/g, '');
            const amount = parseFloat(numericValue) || 0;

            // Format the amount with thousand separators
            const formatter = new Intl.NumberFormat('en-US', {
                maximumFractionDigits: 1,
                minimumFractionDigits: 0
            });

            const formattedAmount = formatter.format(amount);

            // Update the display values
            const elements = document.getElementsByClassName(classname);
            for (let i = 0; i < elements.length; i++) {
                elements[i].textContent = formattedAmount + ' PKR';
            }

        }

    }

    BiometricVerifications() {

        const fileInput = document.getElementById('ImagefileInput');
        const preview = document.getElementById('preview');
        const previewContainer = document.getElementById('preview-container');
        const uploadLabel = document.getElementById('uploadLabel');
        const removeBtn = document.getElementById('removeImage');

        if (previewContainer) {
            previewContainer.style.display = 'none';
            removeBtn.style.display = 'none';
        }

        if (uploadLabel) {
            uploadLabel.style.display = 'block';
        }

        if (preview && preview.src != null && preview.src != "") {
            previewContainer.style.display = 'block';
            removeBtn.style.display = 'block';
            uploadLabel.style.display = 'none';
        }

        if (fileInput) {
            fileInput.addEventListener('change', function (event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function (e) {
                    preview.src = e.target.result;

                    // Show preview, hide label
                    previewContainer.style.display = 'block';
                    uploadLabel.style.display = 'none';
                };
                reader.readAsDataURL(file);
            });
        }

        if (removeBtn) {
            removeBtn.addEventListener('click', function () {
                fileInput.value = ""; // Clear file input
                preview.src = "";
                removeBtn.style.display = 'none';
                previewContainer.style.display = 'none';
                uploadLabel.style.display = 'inline-block';
            });
        }
    }

    DashboardFilters() {
        const dateFrom = document.getElementById('dateFrom');
        if (dateFrom) {
            dateFrom.addEventListener('change', function () {
                const selectedDate = this.value;
                const url = new URL(window.location.href);
                url.searchParams.set('dateFrom', selectedDate);
                window.location.href = url.toString();
            });
        }

        const dateTo = document.getElementById('dateTo');
        if (dateTo) {
            dateTo.addEventListener('change', function () {
                const selectedDate = this.value;
                const url = new URL(window.location.href);
                url.searchParams.set('dateTo', selectedDate);
                window.location.href = url.toString();
            });
        }
    }

    PowerOfAttorny() {
        const powerOfAttorney = document.getElementById('powerOfAttorney');
        if (powerOfAttorney) {
            powerOfAttorney.addEventListener('change', function () {
                const url = new URL(window.location.href);
                const params = url.searchParams;

                if (this.checked) {
                    params.set('showAttorny', 'true');
                } else {
                    params.delete('showAttorny');
                }

                // Set the updated query string back
                url.search = params.toString();
                window.location.href = url.toString();
            });
        }
    }

    StepperFormSubmissions(containerId) {
        const container = document.getElementById(containerId);  // Get the container by ID

        if (!container) return;  // Exit if no container exists

        const stepperForm = document.getElementById("stepper-form");
        const btnNext = document.getElementById("btn-next");
        const combinedData = {};
        let hasInvalid = false;

        // Listen for the "Next" button click
        if (btnNext) {
            btnNext.addEventListener('click', async function () {
                this.showLoader(true);
                const forms = container.querySelectorAll('form');
                const listingsToValidate = document.querySelectorAll('div[data-validate-listing]');
                const filteredForms = [...forms].filter(form => form !== stepperForm);

                let hasInvalid = false;
                let combinedData = {};


                if (listingsToValidate) {
                    for (const listing of listingsToValidate) {
                        const tbody = listing.querySelector('table tbody');
                        const title = listing.dataset.title || 'This section';
                        // If <tbody> exists and has NO rows
                        if (tbody == null || tbody.children.length === 0) {
                            ShowToast({
                                title: 'Error',
                                message: `${title} has no record available.`,
                                status: 'error'
                            });
                            this.showLoader(false);
                            return; // Exit on first empty listing
                        }
                        else {
                            const listingRows = [];
                            const rows = tbody.querySelectorAll('tr');

                            for (const row of rows) {
                                const rowData = [];
                                const tds = row.querySelectorAll('td');
                                tds.forEach(td => {
                                    rowData.push(td.textContent.trim());
                                });
                                // Only validate payment status if the title is "Payments"
                                if (title === 'Payments') {

                                    // Call payment verification API using the first column (PSID)
                                    const psid = rowData[0];
                                    if (psid) {
                                        try {
                                            const baseURL = `${window.location.origin}/`;
                                            var url = baseURL + 'api/verify-payment/' + psid;
                                            const response = await fetch(url);
                                            if (response.ok) {
                                                const paymentStatus = await response.json();

                                                // Check if status is PENDING (1)
                                                if (paymentStatus === 1) {
                                                    ShowToast({
                                                        title: 'Error',
                                                        message: `PSID number ${psid} not verified yet.`,
                                                        status: 'error'
                                                    });
                                                    this.showLoader(false);
                                                    return; // Exit on first pending payment
                                                }
                                            }
                                        } catch (error) {
                                            console.error('Error verifying payment:', error);
                                            ShowToast({
                                                title: 'Error',
                                                message: 'Failed to verify payment status.',
                                                status: 'error'
                                            });
                                            this.showLoader(false);
                                            return;
                                        }
                                    }
                                }
                                else if (title === "Verifications") {
                                    const allowablesArray = listing.dataset.allowables.split(',');

                                    // Use for...of loop instead of forEach to properly handle async/await
                                    for (const item of allowablesArray) {
                                        let data = null;

                                        const inputMap = {
                                            "CameraCapture": "picture",
                                            "Biometric": "biometric"
                                        };

                                        if (inputMap[item]) {
                                            const inputElement = document.getElementById(inputMap[item]);
                                            if (inputElement) {
                                                data = inputElement.value;
                                            } else {
                                                ShowToast({
                                                    title: 'Error',
                                                    message: `${item} verification failed for ${rowData[1]}`,
                                                    status: 'error'
                                                });
                                                this.showLoader(false);
                                                return;
                                            }
                                        }

                                        if (data != null) {
                                            try {
                                                const baseURL = `${window.location.origin}/`;
                                                var url = baseURL + 'api/verify-parties/' + data;
                                                console.log(`Calling verification API: ${url}`);

                                                const response = await fetch(url);
                                                console.log(`API Response Status: ${response.status} ${response.statusText}`);

                                                if (response.ok) {
                                                    const verificationStatus = await response.json();
                                                    console.log(`Verification Status for ${item}:`, verificationStatus);

                                                    if (verificationStatus === false) {
                                                        ShowToast({
                                                            title: 'Error',
                                                            message: `${item} verification failed.`,
                                                            status: 'error'
                                                        });
                                                        this.showLoader(false);
                                                        return;
                                                    }
                                                } else {
                                                    console.error(`API call failed with status: ${response.status}`);
                                                    const errorText = await response.text();
                                                    console.error(`Error response:`, errorText);

                                                    ShowToast({
                                                        title: 'Error',
                                                        message: `${item} verification API call failed: ${response.status}`,
                                                        status: 'error'
                                                    });
                                                    this.showLoader(false);
                                                    return;
                                                }
                                            } catch (error) {
                                                console.error(`Error verifying ${item} data:`, error);
                                                ShowToast({
                                                    title: 'Error',
                                                    message: `Failed to verify ${item}: ${error.message}`,
                                                    status: 'error'
                                                });
                                                this.showLoader(false);
                                                return;
                                            }
                                        }
                                    }
                                }
                                listingRows.push(rowData);
                            }

                            if (listingRows.length > 0) {
                                combinedData[title] = listingRows; // title used as key
                            }
                        }
                    }
                }

                // Step 2: 
                // Scenario 1: If form is independent then validate form, post the form or throw error
                for (const form of filteredForms) {

                    const handlerInput = form.querySelector('input[name="_handler"]');
                    const handler = handlerInput?.value || form.getAttribute("id") || form.getAttribute("name") || `form_${Math.random()}`;

                    // Try to find linked listing by handler
                    const linkedListing = document.querySelector(`[data-form-listing="${handler}"]`);
                    const tbody = linkedListing?.querySelector('table tbody');

                    if (linkedListing && tbody && tbody.children.length > 0) {

                        // Case: Form has linked listing with rows -> serialize listing only
                        const listingRows = [];
                        const rows = tbody.querySelectorAll('tr');
                        rows.forEach(row => {
                            const rowData = [];
                            const tds = row.querySelectorAll('td');
                            tds.forEach(td => {
                                rowData.push(td.textContent.trim());
                            });
                            listingRows.push(rowData);
                        });

                        if (listingRows.length > 0) {
                            console.log(linkedListing.dataset.title);

                            // Validate "Selected Denominations" before processing
                            if (linkedListing.dataset.title === "Selected Denominations") {
                                let totalSum = 0;

                                // Loop through tbody rows to calculate sum of amounts
                                const rows = tbody.querySelectorAll('tr');
                                rows.forEach(row => {
                                    const amountCell = row.querySelector('td.amount');
                                    if (amountCell) {
                                        const cellText = amountCell.textContent.trim();
                                        if (cellText) {
                                            // Remove currency symbols, commas, and other formatting
                                            const cleanedText = cellText.replace(/[^\d.-]/g, '');
                                            const numericValue = parseFloat(cleanedText);

                                            // Add to total if it's a valid number
                                            if (!isNaN(numericValue)) {
                                                totalSum += numericValue;
                                            }
                                        }
                                    }
                                });

                                // Check if sum meets minimum threshold
                                if (totalSum < 100) {
                                    ShowToast({
                                        title: 'Error',
                                        message: 'The total amount must be greater than Rs.100 to proceed',
                                        status: 'error'
                                    });
                                    this.showLoader(false);
                                    return;
                                }
                            }

                            combinedData[handler] = listingRows; // handler used as key
                        }
                        continue; // Skip form submit if listing is valid
                    }

                    if (ValidateFormFields(form)) {
                        ShowToast({
                            title: 'Error',
                            message: 'Form submission failed',
                            status: 'error'
                        });
                        this.showLoader(false);
                        form.submit();

                        hasInvalid = true;
                        return;
                    }

                    const formData = new FormData(form);

                    const response = await fetch(form.action, {
                        method: form.method || "POST",
                        body: formData
                    });

                    if (!response.ok) {
                        hasInvalid = true;
                        ShowToast({
                            title: 'Error',
                            message: 'Form submission failed',
                            status: 'error'
                        });
                        console.error("Form submission failed", response);
                        return;
                    }

                    combinedData[handler] = serializeFormData(form);
                }

                if (hasInvalid) return;

                const hdnStepperFormState = document.getElementById("hdn-stepper-form-state");
                if (hdnStepperFormState) {
                    hdnStepperFormState.value = JSON.stringify(combinedData);  // Store serialized data
                }


                stepperForm.submit();  // Now submit the stepper form
                this.showLoader(false);
            }.bind(this));
        }


        function ValidateFormFields(form) {
            const elements = form.elements;

            for (let i = 0; i < elements.length; i++) {
                const el = elements[i];
                const tag = el.tagName.toLowerCase();

                // Only check fields that are required
                if (!el.hasAttribute("field-required")) {
                    continue;
                }

                // Skip disabled, hidden, button, or submit fields
                if (
                    el.disabled ||
                    el.type === "hidden" ||
                    el.type === "button" ||
                    el.type === "submit" ||
                    tag === "fieldset"
                ) {
                    continue;
                }

                // For select fields, consider "0" or "" as invalid if required
                if (tag === "select" && el.hasAttribute("field-required") && (el.value === "" || el.value === "0")) {
                    return true;
                }

                // For checkboxes/radios: require checked only if required
                if ((el.type === "checkbox" || el.type === "radio") && el.hasAttribute("field-required") && !el.checked) {
                    return true;
                }

                // For text-based inputs and textareas, only check if required
                if ((tag === "input" || tag === "textarea") && el.hasAttribute("field-required") && el.value.trim() === "") {
                    return true;
                }
            }
            return false;
        }


        function serializeFormData(formElement) {
            const handler = formElement.value;
            const listing = document.querySelector(`[data-form-listing="${formElement.value}"]`);
            const hasListing = listing && listing.querySelector('table tbody tr'); // Check if there are rows in the listing

            const combinedData = {};

            if (hasListing) {
                // Serialize the listing rows only
                const listingRows = [];
                const rows = listing.querySelectorAll('table tbody tr');

                rows.forEach(row => {
                    const rowData = {};
                    const inputs = row.querySelectorAll('input, select, textarea'); // Serialize input fields in the row
                    inputs.forEach(input => {
                        const name = input.name || input.id;
                        if (name) {
                            rowData[name] = input.value;
                        }
                    });
                    listingRows.push(rowData);
                });

                // Store serialized listing rows in the combinedData object
                combinedData[handler] = listingRows;
            } else {
                // Serialize the form data if no listing is found
                const formData = new FormData(formElement);
                const jsonObject = {};

                for (let [key, value] of formData.entries()) {
                    if (key === "__RequestVerificationToken") continue;
                    if (key === "_handler") key = "handler";

                    if (jsonObject.hasOwnProperty(key)) {
                        if (!Array.isArray(jsonObject[key])) {
                            jsonObject[key] = [jsonObject[key]];
                        }
                        jsonObject[key].push(value);
                    } else {
                        jsonObject[key] = value;
                    }
                }

                combinedData[handler] = jsonObject;
            }

            return combinedData;
        }
    }

    PrintPreview() {
        const printPreview = document.getElementById('print-preview');
        if (printPreview) {
            printPreview.addEventListener('click', function () {
                const downloadButton = document.getElementById('print-preview');
                if (downloadButton) {
                    downloadButton.style.display = 'none';
                }

                const stepperFormElements = document.getElementById('stepper-form');
                if (stepperFormElements) {
                    stepperFormElements.style.display = 'none';
                }

                printJS({
                    printable: 'printJS-form',
                    type: 'html',
                    css: ['css/bootstrap.min.css', 'css/stampprint.css'],
                    scanStyles: false, // This can help with Bootstrap conflicts
                    targetStyles: ['*'] // Include all styles 
                });

                downloadButton.style.display = '';
                stepperFormElements.style.display = '';

            });
        }
    }

    InitializeCamera() {
        const form = document.getElementById('camera-picture-capture');
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const captureButton = document.getElementById('capture');
        const resetButton = document.getElementById('reset');
        const imageBase64Input = document.getElementById('imageBase64');
        const contentType = document.getElementById('contentType');
        const size = document.getElementById('size');
        if (form) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then((stream) => {
                    video.srcObject = stream;
                    this.cameraStream = stream;
                })
                .catch(function (err) {
                    console.log("Error accessing camera: ", err);
                });

            if (captureButton) {
                captureButton.onclick = () => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const context = canvas.getContext('2d');
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    canvas.style.display = 'block';
                    video.style.display = 'none';

                    if (this.cameraStream) {
                        this.cameraStream.getTracks().forEach(track => track.stop());
                    }

                    // Convert canvas to base64 string
                    const base64Image = canvas.toDataURL('image/jpeg', 0.95);

                    // Remove the data:image/jpeg;base64, prefix
                    const base64Data = base64Image.replace(/^data:image\/jpeg;base64,/, '');

                    // Set the value of the hidden input field
                    if (imageBase64Input) {
                        imageBase64Input.value = base64Data;
                    }

                    // === ACCURATE SIZE USING BLOB ===
                    fetch(base64Image)
                        .then(res => res.blob())
                        .then(blob => {
                            console.log('Content Type:', blob.type);
                            console.log('Image Size (bytes):', blob.size);
                            if (contentType && size) {
                                contentType.value = blob.type;
                                size.value = blob.size;
                            }
                        })
                        .catch(err => {
                            console.error('Error creating Blob from Base64:', err);
                        });
                };
            }

            if (resetButton) {
                resetButton.onclick = () => {
                    canvas.style.display = 'none';
                    video.style.display = 'block';

                    // Reset the hidden input
                    if (imageBase64Input) {
                        imageBase64Input.value = '';
                    }

                    this.InitializeCamera();
                };
            }
        }
    }

    InitializeBiometric() {
        const form = document.getElementById('nadra-biometric-form');
        if (form) {
            var fpObject = null;
            fpObject = new FingerprintSdk();
            fpObject.getReaders();
            setButtonStates(false); // Initial state

            function refreshReader() {
                fpObject.stop();
                document.getElementById("fpImage").src = "/images/biometric-placeholder.png";

                fpObject.getReaders().then(() => {
                    fpObject.start();
                });
            }



            var start = document.getElementById("captureBtn");
            if (start) {
                start.addEventListener('click', function () {
                    fpObject.start();
                });
            }

            var refreshBtn = document.getElementById("refreshBtn");
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function () {
                    refreshReader();
                });
            }
        }
    }

    DropdownsSelectionChanged() {

        const baseURL = `${window.location.origin}/`;
        const ddlDistrict = document.getElementById("ddlDistrict");
        if (ddlDistrict && ddlDistrict.hasAttribute("data-fetch-tehsils")) {
            ddlDistrict.addEventListener('change', async function () {
                const dropdown = document.getElementById('ddlTehsil');

                // Clear the dropdown and show a loading message
                dropdown.innerHTML = '<option value="0">انتظار کیجیے</option>';

                // Construct the URL
                var url = baseURL + 'api/gettehsilbydistrictid/' + this.value;

                // Fetch data from the server
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok ' + response.statusText);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Clear the loading message
                        dropdown.innerHTML = '<option value="0">تحصیل منتخب کریں</option>';

                        // Populate the dropdown with data
                        data.forEach(item => {
                            const option = document.createElement('option');
                            option.value = item.intValue;
                            option.textContent = item.text;
                            dropdown.appendChild(option);
                        });
                    })
                    .catch(error => {
                        // Handle errors and display an error message in the dropdown
                        console.error('There was a problem with the fetch operation:', error);
                        dropdown.innerHTML = '<option value="0">تحصیلیں لوڈ نہیں ہو سکیں</option>';
                    });
            });
        }

        const ddlTehsil = document.getElementById("ddlTehsil");
        if (ddlTehsil && ddlTehsil.hasAttribute("data-fetch-mauzas")) {
            ddlTehsil.addEventListener('change', async function () {
                const dropdown = document.getElementById('ddlMauza');

                // Clear the dropdown and show a loading message
                dropdown.innerHTML = '<option value="0">انتظار کیجیے</option>';

                // Construct the URL
                var url = baseURL + 'api/getmauzabytehsilid/' + this.value;

                // Fetch data from the server
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok ' + response.statusText);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Clear the loading message
                        dropdown.innerHTML = '<option value="0">موضع منتخب کریں</option>';

                        // Populate the dropdown with data
                        data.forEach(item => {
                            const option = document.createElement('option');
                            option.value = item.intValue;
                            option.textContent = item.text;
                            dropdown.appendChild(option);
                        });
                    })
                    .catch(error => {
                        // Handle errors and display an error message in the dropdown
                        console.error('There was a problem with the fetch operation:', error);
                        dropdown.innerHTML = '<option value="0">موضع لوڈ کرنے میں ناکامی ہوئی</option>';
                    });
            });
        }

    }

    UploadFile() {
        const fileInput = document.getElementById('file-input');
        const fileNameField = document.getElementById('fileName');
        const fileNameDisplay = document.getElementById('selected-resource');
        if (fileInput) {
            fileInput.addEventListener('change', function () {
                if (fileInput.files.length > 0) {
                    const fileName = fileInput.files[0].name;
                    fileNameField.value = fileName;
                    fileNameDisplay.textContent = `${fileName}`;
                } else {
                    fileNameDisplay.textContent = '';
                }
            });
        }
    }

    CalculateStampDenominationAmount() {
        const denominations = document.getElementById("denominations");
        const amount = document.getElementById("amount");
        const noofstamops = document.getElementById("noofstamops");
        if (denominations) {
            denominations.addEventListener('change', function () {
                amount.value = noofstamops.value * this.value;
            });
        }

        if (noofstamops) {
            $(noofstamops).on('change keydown paste input', function () {
                amount.value = $(this).val() * denominations.value;
            });
        }
    }

}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    new eSoRSEvents();
});

var FingerprintSdk = (function () {
    function FingerprintSdk() {

        this.currentFormat = Fingerprint.SampleFormat.Raw;
        this.currentValue = "";

        var _this = this;
        this.acquisitionStarted = false;
        this.sdk = new Fingerprint.WebApi();

        this.sdk.onDeviceConnected = function () {
            showStatus("Device connected.");
        };
        this.sdk.onDeviceDisconnected = function () {
            showStatus("Device disconnected.");
        };
        this.sdk.onCommunicationFailed = function () {
            showStatus("Communication with device failed.");
        };
        this.sdk.onSamplesAcquired = async function (s) {
            var samples = JSON.parse(s.samples);
            var sampleData = Fingerprint.b64UrlTo64(samples[0].Data);
            var decodedData = JSON.parse(Fingerprint.b64UrlToUtf8(sampleData));
            var base64RawImage = Fingerprint.b64UrlTo64(decodedData.Data);

            var impressionHeight = decodedData.Format.iHeight;
            var impressionWidth = decodedData.Format.iWidth;
            var impressionResolution = decodedData.Format.iXdpi;
            var canvas = null;
            var context = null;
            canvas = document.getElementById("myCanvas");
            context = canvas.getContext("2d");

            drawToCanvas(base64RawImage);

            //var imageSrc = "data:image/png;base64," + Fingerprint.b64UrlTo64(samples[0]);
            //document.getElementById("fpImage").src = imageSrc;
            var cnic = document.getElementById("cnic").value;
            var party = document.getElementById("party").value;
            var task = document.getElementById("task").value;
            showStatus("Fingerprint captured.");
            await verifyFingerprint(cnic, party, task, base64RawImage, impressionWidth, impressionHeight, impressionResolution)

            function drawToCanvas (binaryArray) {

                var width = impressionWidth,
                    height = impressionHeight;

                var srcIndex = 0, dstIndex = 0, curPixelNum = 0;
                var unicodeArray = [];

                var DecodeRawData = atob(binaryArray);

                for (var i = 0; i < DecodeRawData.length; i++) {
                    unicodeArray.push(DecodeRawData.charCodeAt(i));
                }

                var rgbData = new Uint8Array(unicodeArray);

                canvas.width = width;
                canvas.height = height;

                var mImgData = context.createImageData(width, height);

                for (curPixelNum = 0; curPixelNum < width * height; curPixelNum++) {
                    mImgData.data[dstIndex] = rgbData[srcIndex];        // r
                    mImgData.data[dstIndex + 1] = rgbData[srcIndex + 1];    // g
                    mImgData.data[dstIndex + 2] = rgbData[srcIndex + 2];    // b
                    mImgData.data[dstIndex + 3] = 255; // 255 = 0xFF - constant alpha, 100% opaque
                    srcIndex += 1;
                    dstIndex += 4;
                }
                context.putImageData(mImgData, 0, 0);
                document.getElementById("fpImage").src = canvas.toDataURL("image/jpeg", 0.2);

            };
        };
    }

    async function verifyFingerprint(cnic, party, task, fingerTemplate, impressionWidth, impressionHeight, impressionResolution) {
        try {
            showStatus("Verifying fingerprint with NADRA...");

            const requestBody = {
                cnic: cnic,
                party: party,
                task: task,
                fingerTemplate: fingerTemplate,
                width: impressionWidth,
                height: impressionHeight,
                resolution: impressionResolution
            };

            const response = await fetch('/api/verify-fingerprint', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('LRMIS-style NADRA Verification Response:', result);

                // Populate the hidden field with NADRA response data
                const hiddenField = document.getElementById("nadra-biometric-response");
                if (hiddenField && result.NadraBiometricData) {
                    hiddenField.value = result.NadraBiometricData;
                    console.log('Hidden field populated with LRMIS-style NADRA data');
                }

                // Update status based on LRMIS-style verification result
                if (result.Success && result.IsVerified) {
                    let statusMessage = `✅ NADRA Verification Successful!`;
                    if (result.CitizenName) {
                        statusMessage += ` Citizen: ${result.CitizenName}`;
                    }
                    if (result.FatherName) {
                        statusMessage += ` S/O: ${result.FatherName}`;
                    }
                    if (result.StatusCode) {
                        statusMessage += ` (Code: ${result.StatusCode})`;
                    }
                    showStatus(statusMessage);

                    // LRMIS-style success handling
                    const form = document.getElementById('nadra-biometric-form');
                    if (form) {
                        form.classList.remove('verification-failed');
                        form.classList.add('verification-success');
                    }

                    // Enable save button with LRMIS-style feedback
                    const saveButton = document.getElementById('btnSave');
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.classList.remove('btn-secondary');
                        saveButton.classList.add('btn-primary');
                        saveButton.innerHTML = '<i class="fas fa-save"></i> Save Verification';
                    }

                    // Update status element with success styling
                    const statusElement = document.getElementById('status');
                    if (statusElement) {
                        statusElement.className = 'alert alert-success';
                        statusElement.innerHTML = `<i class="fas fa-check-circle"></i> ${statusMessage}`;
                    }

                    console.log('LRMIS-style verification successful:', result);
                } else {
                    const errorMessage = `❌ Verification failed: ${result.Message || 'Unknown error'}`;
                    showStatus(errorMessage);

                    // LRMIS-style error handling
                    const form = document.getElementById('nadra-biometric-form');
                    if (form) {
                        form.classList.remove('verification-success');
                        form.classList.add('verification-failed');
                    }

                    // Keep save button disabled with LRMIS-style feedback
                    const saveButton = document.getElementById('btnSave');
                    if (saveButton) {
                        saveButton.disabled = true;
                        saveButton.classList.remove('btn-primary');
                        saveButton.classList.add('btn-secondary');
                        saveButton.innerHTML = '<i class="fas fa-save"></i> Save Verification';
                    }

                    // Update status element with error styling
                    const statusElement = document.getElementById('status');
                    if (statusElement) {
                        statusElement.className = 'alert alert-danger';
                        statusElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${errorMessage}`;
                    }

                    console.log('LRMIS-style verification failed:', result);
                }

                // Store LRMIS-style verification result for form validation
                window.nadraVerificationResult = result;
                window.lrmisVerificationComplete = result.Success && result.IsVerified;

            } else {
                const errorText = await response.text();
                console.error('Error verifying fingerprint:', response.status, errorText);
                showStatus(`Verification failed: HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Error during fingerprint verification:', error);
            showStatus(`Verification error: ${error.message}`);
        }
    }

    function showStatus(msg) {
        document.getElementById("status").innerText = msg;
    }

    FingerprintSdk.prototype.start = function () {
        if (this.acquisitionStarted) return;
        var _this = this;
        this.sdk.startAcquisition(this.currentFormat, this.currentValue).then(function () {
            _this.acquisitionStarted = true;
            showStatus("Place your thumb on the reader.");
        }, function (error) {
            showStatus("Start error: " + error.message);
        });
    };

    FingerprintSdk.prototype.stop = function () {
        if (!this.acquisitionStarted) return;
        var _this = this;
        this.sdk.stopAcquisition().then(function () {
            _this.acquisitionStarted = false;
            showStatus("Capture stopped.");
            setButtonStates(false);
        }, function (error) {
            showStatus("Stop error: " + error.message);
        });
    };

    FingerprintSdk.prototype.getReaders = function () {
        var _this = this;
        return this.sdk.enumerateDevices().then(function (devices) {
            if (devices.length > 0) {
                myVal = devices[0];
                showStatus("Reader detected: " + myVal);
            } else {
                showStatus("No fingerprint readers found.");
            }
        });
    };

    return FingerprintSdk;
})();
function ShowToast({ title = '', message = '', status = '' }) {
    // Remove existing toast if any
    const oldToast = document.getElementById('liveToast');
    if (oldToast) oldToast.remove();

    // Create toast container
    const toastContainer = document.querySelector('.toast-container') || (() => {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    })();

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.id = 'liveToast';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // Header color classes
    const headerBgClass = status === 'success' ? 'bg-success' : 'bg-danger';

    toast.innerHTML = `
        <div class="toast-header ${headerBgClass} text-white">
        <i class="fa-solid fa-xmark rounded me-2"></i>
            <strong class="me-auto">${title}</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    toastContainer.appendChild(toast);

    // Show toast
    const toastInstance = bootstrap.Toast.getOrCreateInstance(toast);
    toastInstance.show();
}

function setButtonStates(isCapturing) {
    document.getElementById("captureBtn").disabled = isCapturing;
    document.getElementById("refreshBtn").disabled = !isCapturing;
    document.getElementById("btnSave").disabled = !isCapturing;
}

function ValidatePayments() {

}




