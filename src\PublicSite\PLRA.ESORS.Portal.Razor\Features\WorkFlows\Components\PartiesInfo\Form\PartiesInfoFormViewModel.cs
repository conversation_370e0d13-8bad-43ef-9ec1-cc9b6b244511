﻿using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using System.ComponentModel.DataAnnotations;
namespace PLRA.ESORS.Portal.Razor.Features.PartiesInfo;
public class PartiesInfoFormViewModel : ObservableBase
{
    public PartiesInfoFormViewModel()
    {
        //StrCNIC = string.Empty;
        //StrPhoneNumber = string.Empty;

    }
    public long? Appealent_Id { get; set; }
    public long EStampMainId { get; set; }
    [Required(ErrorMessage = "Name is required")]
    [StringLength(150, ErrorMessage = "Name cannot exceed 150 characters")]
    public string? Appealent_Name { get; set; }
    [Required(ErrorMessage = "Relation is required")]
    [StringLength(100, ErrorMessage = "Relation cannot exceed 100 characters")]
    public string? Appealent_Relation { get; set; }
    [Required(ErrorMessage = "Relative name is required")]
    [StringLength(150, ErrorMessage = "Relative name cannot exceed 150 characters")]
    public string? Appealent_RelativeName { get; set; }

    [EmailAddress]
    [Required(ErrorMessage = "Email is required")]
    [StringLength(200, ErrorMessage = "Email cannot exceed 200 characters")]
    public string? Appealent_Email { get; set; }
    [Required(ErrorMessage = "Address is required")]
    [StringLength(200, ErrorMessage = "Address cannot exceed 200 characters")]
    public string? Appealent_Address { get; set; }
    public string? Appealent_LawyerName { get; set; }

    /// <summary>
    /// JSON metadata storing which fields were populated from API vs manually entered
    /// </summary>
    public string? ApiFieldMetadata { get; set; }

    //public PARTY_TYPE Party_Type { get; set; }

    [Required(ErrorMessage = "CNIC is required")]
    [Range(1000000000000, 9999999999999, ErrorMessage = "CNIC must be 13 digits.")]
    public long? Appealent_CNIC { get; set; }

    [Required(ErrorMessage = "PhoneNumber is required")]
    public long? Appealent_PhoneNumber { get; set; }

    public PARTY_TYPE PARTY_TYPE { get; set; }
}
