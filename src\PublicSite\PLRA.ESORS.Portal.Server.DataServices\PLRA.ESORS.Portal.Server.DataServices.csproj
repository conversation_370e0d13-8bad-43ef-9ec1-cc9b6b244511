﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Features\WorkFlows\Components\PartiesInfo\Listing\**" />
    <EmbeddedResource Remove="Features\WorkFlows\Components\PartiesInfo\Listing\**" />
    <None Remove="Features\WorkFlows\Components\PartiesInfo\Listing\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Features\WorkFlows\Components\PartiesInfo\Listing\PartiesInfoServerSideListingDataService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="9.0.1" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\PLRA.ESORS.Framework\PLRA.ESORS.Framework.csproj" />
    <ProjectReference Include="..\..\PLRA.ESORS.Server.Data\PLRA.ESORS.Server.Data.csproj" />
    <ProjectReference Include="..\..\PLRA.ESORS.ServiceContracts\PLRA.ESORS.ServiceContracts.csproj" />
    <ProjectReference Include="..\PLRA.ESORS.Portal.ServiceContracts\PLRA.ESORS.Portal.ServiceContracts.csproj" />
  </ItemGroup>


</Project>
