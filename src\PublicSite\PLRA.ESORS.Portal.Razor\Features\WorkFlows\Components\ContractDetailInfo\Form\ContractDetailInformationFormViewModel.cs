﻿using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using System.ComponentModel.DataAnnotations;
namespace PLRA.ESORS.Portal.Razor.Features.ContractDetailInformation;
public class ContractDetailInformationFormViewModel : ObservableBase
{
    public long? Id { get; set; }
    public long EStampMainId { get; set; }

    [Range(1, 2, ErrorMessage = "Contract Type must be selected")]
    public CONTRACT_TYPE CONTRACT_TYPE { get; set; }

    [Required(ErrorMessage = "Title is required")]
    [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
    public string? Title { get; set; }

    [Required(ErrorMessage = "Type is required")]
    [StringLength(150, ErrorMessage = "Type cannot exceed 150 characters")]
    public string? Type { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Scope must be at least 1")]
    public int Scope { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Amount must be at least 1")]
    public int Amount { get; set; }

    [Required(ErrorMessage = "Duration is required")]
    [StringLength(150, ErrorMessage = "Duration cannot exceed 150 characters")]
    public string? Duration { get; set; }

    [Required(ErrorMessage = "From is required")]
    public DateTime? From { get; set; }

    [Required(ErrorMessage = "To is required")]
    public DateTime? To { get; set; }

    [Required(ErrorMessage = "Terms is required")]
    [StringLength(150, ErrorMessage = "Terms cannot exceed 150 characters")]
    public string? Terms { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Please select valid District.")]
    public int DistrictId { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Please select valid Tehsil.")]
    public int TehsilId { get; set; }

    public CONTRACT_CLAUSES CONTRACT_CLAUSES { get; set; }

}
