<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://NADRA.Biometric.Verification" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:i0="http://NADRA.Biometric.Verification1" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="BioVeriSysGeneric" targetNamespace="http://NADRA.Biometric.Verification">
<wsdl:types>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://NADRA.Biometric.Verification">
<xs:import namespace="http://schemas.datacontract.org/2004/07/NADRA.Biometric.Verification"/>
<xs:element name="VerifyFingerPrints">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="franchizeID" nillable="true" type="xs:string"/>
<xs:element minOccurs="0" name="xml_request_data" nillable="true" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="VerifyFingerPrintsResponse">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="VerifyFingerPrintsResult" nillable="true" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="GetLastVerificationResults">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="franchizeID" nillable="true" type="xs:string"/>
<xs:element minOccurs="0" name="xml_request_data" nillable="true" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="GetLastVerificationResultsResponse">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="GetLastVerificationResultsResult" nillable="true" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="TestService">
<xs:complexType>
<xs:sequence>
<xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/NADRA.Biometric.Verification" minOccurs="0" name="type" type="q1:TemplateType"/>
<xs:element minOccurs="0" name="tempType" nillable="true" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="TestServiceResponse">
<xs:complexType>
<xs:sequence>
<xs:element minOccurs="0" name="TestServiceResult" nillable="true" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:element>
</xs:schema>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/">
<xs:element name="anyType" nillable="true" type="xs:anyType"/>
<xs:element name="anyURI" nillable="true" type="xs:anyURI"/>
<xs:element name="base64Binary" nillable="true" type="xs:base64Binary"/>
<xs:element name="boolean" nillable="true" type="xs:boolean"/>
<xs:element name="byte" nillable="true" type="xs:byte"/>
<xs:element name="dateTime" nillable="true" type="xs:dateTime"/>
<xs:element name="decimal" nillable="true" type="xs:decimal"/>
<xs:element name="double" nillable="true" type="xs:double"/>
<xs:element name="float" nillable="true" type="xs:float"/>
<xs:element name="int" nillable="true" type="xs:int"/>
<xs:element name="long" nillable="true" type="xs:long"/>
<xs:element name="QName" nillable="true" type="xs:QName"/>
<xs:element name="short" nillable="true" type="xs:short"/>
<xs:element name="string" nillable="true" type="xs:string"/>
<xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte"/>
<xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt"/>
<xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong"/>
<xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort"/>
<xs:element name="char" nillable="true" type="tns:char"/>
<xs:simpleType name="char">
<xs:restriction base="xs:int"/>
</xs:simpleType>
<xs:element name="duration" nillable="true" type="tns:duration"/>
<xs:simpleType name="duration">
<xs:restriction base="xs:duration">
<xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?"/>
<xs:minInclusive value="-P10675199DT2H48M5.4775808S"/>
<xs:maxInclusive value="P10675199DT2H48M5.4775807S"/>
</xs:restriction>
</xs:simpleType>
<xs:element name="guid" nillable="true" type="tns:guid"/>
<xs:simpleType name="guid">
<xs:restriction base="xs:string">
<xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
</xs:restriction>
</xs:simpleType>
<xs:attribute name="FactoryType" type="xs:QName"/>
<xs:attribute name="Id" type="xs:ID"/>
<xs:attribute name="Ref" type="xs:IDREF"/>
</xs:schema>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/NADRA.Biometric.Verification" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/NADRA.Biometric.Verification">
<xs:simpleType name="TemplateType">
<xs:restriction base="xs:string">
<xs:enumeration value="ANSI"/>
<xs:enumeration value="ISO_19794_2"/>
<xs:enumeration value="SAGEM_PKMAT"/>
<xs:enumeration value="SAGEM_PKCOMPV2"/>
<xs:enumeration value="SAGEM_CFV"/>
<xs:enumeration value="RAW_IMAGE"/>
<xs:enumeration value="WSQ"/>
</xs:restriction>
</xs:simpleType>
<xs:element name="TemplateType" nillable="true" type="tns:TemplateType"/>
</xs:schema>
</wsdl:types>
<wsdl:message name="IBioVeriSysGeneric_VerifyFingerPrints_InputMessage">
<wsdl:part name="parameters" element="tns:VerifyFingerPrints"/>
</wsdl:message>
<wsdl:message name="IBioVeriSysGeneric_VerifyFingerPrints_OutputMessage">
<wsdl:part name="parameters" element="tns:VerifyFingerPrintsResponse"/>
</wsdl:message>
<wsdl:message name="IBioVeriSysGeneric_GetLastVerificationResults_InputMessage">
<wsdl:part name="parameters" element="tns:GetLastVerificationResults"/>
</wsdl:message>
<wsdl:message name="IBioVeriSysGeneric_GetLastVerificationResults_OutputMessage">
<wsdl:part name="parameters" element="tns:GetLastVerificationResultsResponse"/>
</wsdl:message>
<wsdl:message name="IBioVeriSysGeneric_TestService_InputMessage">
<wsdl:part name="parameters" element="tns:TestService"/>
</wsdl:message>
<wsdl:message name="IBioVeriSysGeneric_TestService_OutputMessage">
<wsdl:part name="parameters" element="tns:TestServiceResponse"/>
</wsdl:message>
<wsdl:portType name="IBioVeriSysGeneric">
<wsdl:operation name="VerifyFingerPrints">
<wsdl:input wsaw:Action="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/VerifyFingerPrints" message="tns:IBioVeriSysGeneric_VerifyFingerPrints_InputMessage"/>
<wsdl:output wsaw:Action="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/VerifyFingerPrintsResponse" message="tns:IBioVeriSysGeneric_VerifyFingerPrints_OutputMessage"/>
</wsdl:operation>
<wsdl:operation name="GetLastVerificationResults">
<wsdl:input wsaw:Action="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/GetLastVerificationResults" message="tns:IBioVeriSysGeneric_GetLastVerificationResults_InputMessage"/>
<wsdl:output wsaw:Action="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/GetLastVerificationResultsResponse" message="tns:IBioVeriSysGeneric_GetLastVerificationResults_OutputMessage"/>
</wsdl:operation>
<wsdl:operation name="TestService">
<wsdl:input wsaw:Action="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/TestService" message="tns:IBioVeriSysGeneric_TestService_InputMessage"/>
<wsdl:output wsaw:Action="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/TestServiceResponse" message="tns:IBioVeriSysGeneric_TestService_OutputMessage"/>
</wsdl:operation>
</wsdl:portType>
<wsdl:binding name="BasicHttpBinding_IBioVeriSysGeneric" type="tns:IBioVeriSysGeneric">
<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
<wsdl:operation name="VerifyFingerPrints">
<soap:operation soapAction="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/VerifyFingerPrints" style="document"/>
<wsdl:input>
<soap:body use="literal"/>
</wsdl:input>
<wsdl:output>
<soap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="GetLastVerificationResults">
<soap:operation soapAction="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/GetLastVerificationResults" style="document"/>
<wsdl:input>
<soap:body use="literal"/>
</wsdl:input>
<wsdl:output>
<soap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="TestService">
<soap:operation soapAction="http://NADRA.Biometric.Verification/IBioVeriSysGeneric/TestService" style="document"/>
<wsdl:input>
<soap:body use="literal"/>
</wsdl:input>
<wsdl:output>
<soap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
</wsdl:binding>
<wsdl:service name="BioVeriSysGeneric">
<wsdl:port name="BasicHttpBinding_IBioVeriSysGeneric" binding="tns:BasicHttpBinding_IBioVeriSysGeneric">
<soap:address location="http://***********:8086/Nadra/Services/BioVeriSysGeneric"/>
</wsdl:port>
</wsdl:service>
</wsdl:definitions>