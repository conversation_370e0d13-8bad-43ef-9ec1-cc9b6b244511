﻿using PLRA.ESORS.ServiceContracts;

namespace PLRA.ESORS.Server.DataServices.Recaptcha
{
    public class CaptchaService
    {
        private const string AllowedChars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789";
        private readonly Random _random = new Random();
        private string _currentCaptchaText = string.Empty;
        private readonly IErrorLoggingService _errorLoggingService;

        public CaptchaService(IErrorLoggingService errorLoggingService)
        {
            _errorLoggingService = errorLoggingService;
        }

        public string GetCurrentCaptchaText() => _currentCaptchaText;

        public string GenerateCaptchaText(int length = 6)
        {
            try
            {
                var chars = new char[length];

                for (int i = 0; i < length; i++)
                {
                    chars[i] = AllowedChars[_random.Next(0, AllowedChars.Length)];
                }

                _currentCaptchaText = new string(chars);
                return _currentCaptchaText;
            }
            catch (Exception ex)
            {
                _errorLoggingService.LogErrorAsync(
                    nameof(CaptchaService),
                    nameof(GenerateCaptchaText),
                    ex,
                    new { length }
                );
                throw;
            }
        }

        // Generate styling for each character in the CAPTCHA
        public List<CaptchaCharacter> GenerateCharacterStyles(string captchaText = null)
        {
            if (string.IsNullOrEmpty(captchaText))
            {
                captchaText = _currentCaptchaText;
            }

            var characters = new List<CaptchaCharacter>();

            for (int i = 0; i < captchaText.Length; i++)
            {
                characters.Add(new CaptchaCharacter
                {
                    Character = captchaText[i].ToString(),
                    RotationDeg = _random.Next(-15, 15),
                    OffsetY = _random.Next(-8, 8),
                    SkewX = _random.Next(-10, 10) * 0.01,
                    SkewY = _random.Next(-5, 5) * 0.01
                });
            }

            return characters;
        }

        // Validate the user's input against the current CAPTCHA
        public bool ValidateCaptcha(string userInput)
        {
            if (string.IsNullOrEmpty(_currentCaptchaText) || string.IsNullOrEmpty(userInput))
                return false;

            return _currentCaptchaText.Equals(userInput, StringComparison.OrdinalIgnoreCase);
        }
    }

    public class CaptchaCharacter
    {
        public string Character { get; set; }
        public int RotationDeg { get; set; }
        public int OffsetY { get; set; }
        public double SkewX { get; set; }
        public double SkewY { get; set; }
    }
}
