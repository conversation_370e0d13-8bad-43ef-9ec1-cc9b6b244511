﻿using PLRA.ESORS.Framework.Enums;
namespace PLRA.ESORS.Portal.ServiceContracts.Features.PartiesInfo;
public class PartiesInfoFormBusinessObject
{
    public PartiesInfoFormBusinessObject()
    {
    }
    public long? Appealent_Id { get; set; }
    public long EStampMainId { get; set; }
    public string? Appealent_Name { get; set; }
    public string? Appealent_Relation { get; set; }
    public string? Appealent_RelativeName { get; set; }
    public long? Appealent_CNIC { get; set; }
    public long? Appealent_PhoneNumber { get; set; }
    public string? Appealent_Email { get; set; }
    public string? Appealent_Address { get; set; }
    public string?  Appealent_LawyerName { get; set; }

    /// <summary>
    /// JSON metadata storing which fields were populated from API vs manually entered
    /// </summary>
    public string? ApiFieldMetadata { get; set; }

    public PARTY_TYPE PARTY_TYPE { get; set; }
}
