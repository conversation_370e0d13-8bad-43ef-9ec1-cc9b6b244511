﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PLRA.ESORS.Portal.ServiceContracts;
using PLRA.ESORS.Portal.ServiceContracts.Models;
using PLRA.ESORS.Server.Data.Data;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using static PLRA.ESORS.Framework.Utils.TokenStorageHelper;

namespace PLRA.ESORS.Portal.Server.DataServices.Services.EpayApiService
{
    public class EpayApiService : IEpayApiService
    {
        private readonly HttpClient _httpClient;
        private readonly EpayApiSettings _settings;
        private readonly ApplicationDbContext _context;

        public EpayApiService(ApplicationDbContext context, HttpClient httpClient, IOptions<EpayApiSettings> settings)
        {
            _context = context;
            _httpClient = httpClient;
            _settings = settings.Value;
        }

        public async Task<EPayAPIResponse?> CreatePSID(RequestModel requestModel)
        {
            var token = await GetValidTokenAsync();

            if (requestModel.DistrictID == 0)
            {
                requestModel.DistrictID = 7; // ToDo
            }
           
            var requestUrl = _settings.PSIDUrl;
            var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var json = JsonConvert.SerializeObject(requestModel, new JsonSerializerSettings
            {
                StringEscapeHandling = StringEscapeHandling.Default, // Shows actual Urdu
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            });

            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<EPayAPIResponse>(result);
            }

            var error = await response.Content.ReadAsStringAsync();
            JObject obj = JObject.Parse(error);

            string status = obj["status"]?.ToString()!;
            string message = obj["message"]?.ToString()!;
            throw new Exception($"EPay CreatePSID Error: Status={status}, Message ={message}");

        }

        private async Task<string> GetValidTokenAsync()
        {
            var tokenInfo = LoadToken();
            if (tokenInfo == null || tokenInfo.ExpiryTime <= DateTime.UtcNow)
            {
                tokenInfo = await RequestNewTokenAsync();
                if (tokenInfo != null)
                {
                    SaveToken(tokenInfo);
                }
            }

            return tokenInfo == null ? string.Empty : tokenInfo.AccessToken;
        }

        private async Task<TokenInfo?> RequestNewTokenAsync()
        {
            var authUrl = _settings.AuthUrl;
            var authRequest = new
            {
                clientId = _settings.ClientId,
                clientSecretKey = _settings.ClientSecret
            };

            var json = JsonConvert.SerializeObject(authRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(authUrl, content);
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var authResponse = JsonConvert.DeserializeObject<AuthResponse>(responseContent);

                if (authResponse?.Status != "OK" || authResponse.Content == null || !authResponse.Content.Any())
                    throw new Exception("Failed to retrieve token from auth service.");

                var authContent = authResponse.Content.First();

                var expiryTime = DateTimeOffset.FromUnixTimeMilliseconds(authContent.ExpiryDate).UtcDateTime;

                return new TokenInfo
                {
                    AccessToken = authContent.Token.AccessToken,
                    ExpiryTime = expiryTime
                };
            }

            var error = await response.Content.ReadAsStringAsync();
            JObject obj = JObject.Parse(error);

            string status = obj["status"]?.ToString()!;
            string message = obj["message"]?.ToString()!;
            throw new Exception($"EPay Auth API Error: Status={status}, Message ={message}");
        }

        public async Task<bool> PaymentStatusIntimation(PSIDStatusRequestModel request)
        {
            var payment = await _context.PaymentDetails.FirstOrDefaultAsync(x => x.EStampMainId == request.DeptTransactionId && x.PSID == request.ConsumerNumber);
            if (payment == null) throw new InvalidChallanException("Invalid challan or department id");

            if (payment.Amount != request.AmountPaid) throw new InvalidAmountException("PSID amount is not same as intimated");

            payment.GatewayPaidDate = request.PaidDateTime;
            payment.BankCode = request.BankCode;
            payment.GatewayPSIDStatus = request.PSIDStatus;
            payment.PAYMENT_STATUS = request.PSIDStatus!.ToLower() == "paid" ? Framework.Enums.PAYMENT_STATUS.VERIFIED : Framework.Enums.PAYMENT_STATUS.PENDING;
            await _context.SaveChangesAsync();
            return true;

        }
    }

    public class InvalidChallanException : Exception
    {
        public InvalidChallanException(string message = "Invalid challan number.")
            : base(message) { }
    }

    public class InvalidAmountException : Exception
    {
        public InvalidAmountException(string message = "Invalid amount.")
            : base(message) { }
    }
}
