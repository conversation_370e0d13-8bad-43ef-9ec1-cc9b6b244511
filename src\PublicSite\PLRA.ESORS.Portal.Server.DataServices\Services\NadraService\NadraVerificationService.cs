﻿using Microsoft.Extensions.Options;
using PLRA.ESORS.Portal.ServiceContracts;
using PLRA.ESORS.Portal.ServiceContracts.Models;
using System.Data;
using System.Text;
using System.Text.Json;
using System.Xml;
using Microsoft.Extensions.Logging;
using NadraVerificationService;
using System.ServiceModel;
using PLRA.ESORS.Framework;
using System;

namespace PLRA.ESORS.Portal.Server.DataServices.Services
{
    public class NadraApiService : INadraApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<NadraApiService> _logger;
        private readonly IAuthenticatedUser _authenticatedUser;
        public readonly NadraSettings _nadraSettings;
        public readonly PersonSettings _personSettings;

        // LRMIS-style constants
        private const string DEFAULT_FRANCHISE_ID = "3152";
        private const string DEFAULT_AREA_NAME = "punjab";
        private const byte DEFAULT_FINGER_INDEX = 1; // Thumb

        public NadraApiService(
            HttpClient httpClient,
            IOptions<NadraSettings> nadraSettings,
            IOptions<PersonSettings> personSettings,
            ILogger<NadraApiService> logger,
            IAuthenticatedUser authenticatedUser)
        {
            _httpClient = httpClient;
            _nadraSettings = nadraSettings.Value;
            _personSettings = personSettings.Value;
            _logger = logger;
            _authenticatedUser = authenticatedUser;
        }

        /// <summary>
        /// LRMIS-style NADRA fingerprint verification with enhanced error handling
        /// </summary>
        public async Task<DataSet> VerifyFingerPrintAsync(long cnic, string fingerTemplate, string templateType)
        {
            BioVeriSysGenericClient? client = null;
            DataSet dsResponse = new DataSet();

            try
            {
                _logger.LogInformation("Starting LRMIS-style NADRA biometric verification for CNIC: {CNIC}", cnic);

                // Process fingerprint template following BioVeriSys sample code pattern
                string processedFingerTemplate = ProcessFingerprintTemplate(fingerTemplate);

                // Create WSDL client following LRMIS pattern
                client = new BioVeriSysGenericClient();

                // LRMIS-style finger index conversion
                byte convertedFingerIndex = ConvertFingerIndexForNadra(DEFAULT_FINGER_INDEX);

                // Clean CNIC (remove dashes like LRMIS)
                string cleanCnic = cnic.ToString().Replace("-", "");

                // Generate session and transaction IDs
                string sessionId = "";
                string franchiseId = DEFAULT_FRANCHISE_ID;
                Random random = new Random();
                string transactionId = franchiseId + random.Next(1000000, 9999999).ToString("0000000") + random.Next(10000000, 99999999).ToString("00000000");



                // Build LRMIS-style XML request
                string xmlSource = BuildLrmisStyleRequestXml(cleanCnic, convertedFingerIndex, processedFingerTemplate,
                    DEFAULT_AREA_NAME, sessionId, transactionId, templateType);

                _logger.LogDebug("LRMIS-style NADRA Request XML built for CNIC: {CNIC}", cleanCnic);

                // Call NADRA WSDL service using LRMIS pattern

                string response = await client.VerifyFingerPrintsAsync(franchiseId, xmlSource);

                if (!string.IsNullOrEmpty(response))
                {
                    _logger.LogInformation("Received response from NADRA service for CNIC: {CNIC}", cleanCnic);

                    // Parse response following LRMIS pattern
                    dsResponse.ReadXml(new XmlTextReader(new StringReader(response)));

                    // LRMIS-style response processing
                    ProcessLrmisStyleResponse(dsResponse, cleanCnic);

                    _logger.LogDebug("NADRA Response parsed successfully. Tables count: {TablesCount}", dsResponse.Tables.Count);
                }
                else
                {
                    _logger.LogWarning("Empty response received from NADRA service for CNIC: {CNIC}", cleanCnic);
                    // Return empty dataset like LRMIS does
                    return dsResponse;
                }
            }
            catch (FaultException faultEx)
            {
                _logger.LogError(faultEx, "SOAP fault during NADRA verification for CNIC: {CNIC}", cnic);
                // LRMIS-style error handling - return dataset with error info
                CreateErrorDataSet(dsResponse, "SOAP_FAULT", faultEx.Message);
            }
            catch (CommunicationException commEx)
            {
                _logger.LogError(commEx, "Communication error during NADRA verification for CNIC: {CNIC}", cnic);
                CreateErrorDataSet(dsResponse, "COMMUNICATION_ERROR", commEx.Message);
            }
            catch (TimeoutException timeoutEx)
            {
                _logger.LogError(timeoutEx, "Timeout during NADRA verification for CNIC: {CNIC}", cnic);
                CreateErrorDataSet(dsResponse, "TIMEOUT_ERROR", timeoutEx.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during NADRA biometric verification for CNIC: {CNIC}", cnic);
                CreateErrorDataSet(dsResponse, "SERVICE_DOWN", ex.Message);
            }
            finally
            {
                await SafeCloseWcfClient(client);
            }

            return dsResponse;
        }

        public async Task<PersonDetailModel?> GetPersonDetail(long cnic)
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _personSettings.Authorization);
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("access-key", _personSettings.AccessKey);

            var response = await _httpClient.GetAsync($"{_personSettings.URL}{cnic}");

            if (response.IsSuccessStatusCode)
            {
                var personData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    ReadCommentHandling = JsonCommentHandling.Skip
                };
                var personResponse = JsonSerializer.Deserialize<PersonResponse>(personData, options);
                if (personResponse != null && personResponse.StatusCode == 1)
                {
                    return personResponse.Data;
                }
            }

            return null;
        }

        /// <summary>
        /// LRMIS-style finger index conversion for NADRA
        /// </summary>
        private byte ConvertFingerIndexForNadra(byte fingerIndex)
        {
            // LRMIS conversion logic - typically thumb is index 10
            return fingerIndex switch
            {
                1 => 1,  // Right thumb
                2 => 2,  // Right index
                3 => 3,  // Right middle
                4 => 4,  // Right ring
                5 => 5,  // Right little
                6 => 6,  // Left thumb
                7 => 7,  // Left index
                8 => 8,  // Left middle
                9 => 9,  // Left ring
                10 => 10, // Left little
                _ => 1   // Default to thumb
            };
        }

        /// <summary>
        /// Build LRMIS-style XML request for NADRA following BioVeriSys sample code
        /// </summary>
        private string BuildLrmisStyleRequestXml(string cnic, byte fingerIndex, string base64fingerTemplate,
            string areaName, string sessionId, string transactionId, string templateType)
        {
            // Build XML request exactly matching BioVeriSys sample code structure
            var xmlRequest = $@"<BIOMETRIC_VERIFICATION><USER_VERIFICATION><USERNAME>{SecurityHelper.EscapeXml(_nadraSettings.Username)}</USERNAME><PASSWORD>{SecurityHelper.EscapeXml(_nadraSettings.Password)}</PASSWORD></USER_VERIFICATION> " +
                                $" <REQUEST_DATA> " +
                                $" <TRANSACTION_ID>{transactionId}</TRANSACTION_ID> " +
                                $" <SESSION_ID></SESSION_ID> " +
                                $" <CITIZEN_NUMBER>{cnic}</CITIZEN_NUMBER> " +
                                $" <CONTACT_NUMBER></CONTACT_NUMBER> " +
                                $" <FINGER_INDEX>{fingerIndex}</FINGER_INDEX> " +
                                $" <FINGER_TEMPLATE>{base64fingerTemplate}</FINGER_TEMPLATE> " +
                                $" <TEMPLATE_TYPE>{templateType}</TEMPLATE_TYPE> " +
                                $" <AREA_NAME>{areaName.ToUpper()}</AREA_NAME> " +
                                $" </REQUEST_DATA> " +
                           " </BIOMETRIC_VERIFICATION>";

            return xmlRequest;
        }

        /// <summary>
        /// Process LRMIS-style NADRA response
        /// </summary>
        private void ProcessLrmisStyleResponse(DataSet dsResponse, string cnic)
        {
            try
            {
                if (dsResponse.Tables.Contains("RESPONSE_STATUS"))
                {
                    var statusTable = dsResponse.Tables["RESPONSE_STATUS"];
                    if (statusTable.Rows.Count > 0)
                    {
                        string code = statusTable.Rows[0]["CODE"]?.ToString() ?? "";
                        string message = statusTable.Rows[0]["MESSAGE"]?.ToString() ?? "";

                        _logger.LogInformation("NADRA verification code for CNIC {CNIC}: {Code} - {Message}",
                            cnic, code, message);
                    }
                }

                // Process finger data like LRMIS
                if (dsResponse.Tables.Contains("FINGER"))
                {
                    var fingerTable = dsResponse.Tables["FINGER"];
                    _logger.LogDebug("NADRA response contains {FingerCount} finger records", fingerTable.Rows.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing LRMIS-style NADRA response for CNIC: {CNIC}", cnic);
            }
        }

        /// <summary>
        /// Create error dataset following LRMIS pattern
        /// </summary>
        private void CreateErrorDataSet(DataSet dsResponse, string errorCode, string errorMessage)
        {
            try
            {
                // Create RESPONSE_STATUS table like LRMIS
                var statusTable = new DataTable("RESPONSE_STATUS");
                statusTable.Columns.Add("CODE", typeof(string));
                statusTable.Columns.Add("MESSAGE", typeof(string));

                var row = statusTable.NewRow();
                row["CODE"] = errorCode;
                row["MESSAGE"] = errorMessage;
                statusTable.Rows.Add(row);

                dsResponse.Tables.Add(statusTable);

                _logger.LogDebug("Created error dataset with code: {ErrorCode}", errorCode);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error creating error dataset");
            }
        }

        /// <summary>
        /// Safely close WCF client following LRMIS pattern
        /// </summary>
        private async Task SafeCloseWcfClient(BioVeriSysGenericClient? client)
        {
            if (client == null) return;

            try
            {
                if (client.State == CommunicationState.Faulted)
                {
                    _logger.LogWarning("WCF client is in faulted state, aborting connection");
                    client.Abort();
                }
                else if (client.State == CommunicationState.Opened)
                {
                    _logger.LogDebug("Closing WCF client connection");
                    await client.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error occurred while closing NADRA WCF client, aborting connection");
                try
                {
                    client.Abort();
                }
                catch (Exception abortEx)
                {
                    _logger.LogWarning(abortEx, "Error occurred while aborting NADRA WCF client");
                }
            }
        }

        /// <summary>
        /// Creates and configures NADRA WSDL client
        /// </summary>
        private BioVeriSysGenericClient CreateNadraClient()
        {
            var binding = new BasicHttpBinding(BasicHttpSecurityMode.Transport)
            {
                MaxBufferSize = int.MaxValue,
                MaxReceivedMessageSize = int.MaxValue,
                SendTimeout = TimeSpan.FromMinutes(5),
                ReceiveTimeout = TimeSpan.FromMinutes(5),
                OpenTimeout = TimeSpan.FromMinutes(2),
                CloseTimeout = TimeSpan.FromMinutes(2)
            };

            var endpoint = new EndpointAddress(_nadraSettings.URL);
            var client = new BioVeriSysGenericClient(binding, endpoint);

            _logger.LogDebug("Created NADRA WCF client with endpoint: {Endpoint}", _nadraSettings.URL);
            return client;
        }

        /// <summary>
        /// Builds XML request for NADRA biometric verification
        /// </summary>
        private string BuildRequestXml(long cnic, string sessionId, byte fingerIndex, string fingerTemplate, string areaName)
        {
            var transactionId = Guid.NewGuid().ToString();

            var xmlRequest = $@"<?xml version=""1.0"" encoding=""utf-8""?>
<BIOMETRIC_VERIFICATION>
    <USER_VERIFICATION>
        <USERNAME>{SecurityHelper.EscapeXml(_nadraSettings.Username)}</USERNAME>
        <PASSWORD>{SecurityHelper.EscapeXml(_nadraSettings.Password)}</PASSWORD>
    </USER_VERIFICATION>
    <REQUEST_DATA>
        <SESSION_ID>{SecurityHelper.EscapeXml(sessionId)}</SESSION_ID>
        <TRANSACTION_ID>{SecurityHelper.EscapeXml(transactionId)}</TRANSACTION_ID>
        <CITIZEN_NUMBER>{cnic}</CITIZEN_NUMBER>
        <FINGER_INDEX>{fingerIndex}</FINGER_INDEX>
        <FINGER_TEMPLATE>{SecurityHelper.EscapeXml(fingerTemplate)}</FINGER_TEMPLATE>
        <TEMPLATE_TYPE>ANSI</TEMPLATE_TYPE>
        <AREA_NAME>{SecurityHelper.EscapeXml(areaName)}</AREA_NAME>
    </REQUEST_DATA>
</BIOMETRIC_VERIFICATION>";

            return xmlRequest;
        }

        /// <summary>
        /// Process fingerprint template following BioVeriSys sample code pattern
        /// </summary>
        private string ProcessFingerprintTemplate(string fingerTemplate)
        {
            try
            {
                // If the template is already Base64 encoded, return as is
                if (IsBase64String(fingerTemplate))
                {
                    _logger.LogDebug("Fingerprint template is already Base64 encoded");
                    return fingerTemplate;
                }

                // If it's a file path (like in BioVeriSys sample), read the file
                if (fingerTemplate.Contains("\\") || fingerTemplate.Contains("/"))
                {
                    if (File.Exists(fingerTemplate))
                    {
                        _logger.LogDebug("Reading fingerprint template from file: {FilePath}", fingerTemplate);
                        byte[] fileBytes = File.ReadAllBytes(fingerTemplate);
                        return Convert.ToBase64String(fileBytes);
                    }
                }

                // If it's raw binary data, convert to Base64
                if (fingerTemplate.Length > 0)
                {
                    try
                    {
                        // Try to treat as binary data and convert to Base64
                        byte[] templateBytes = System.Text.Encoding.UTF8.GetBytes(fingerTemplate);
                        string base64Template = Convert.ToBase64String(templateBytes);
                        _logger.LogDebug("Converted fingerprint template to Base64. Original length: {OriginalLength}, Base64 length: {Base64Length}",
                            fingerTemplate.Length, base64Template.Length);
                        return base64Template;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to convert fingerprint template to Base64, using original");
                        return fingerTemplate;
                    }
                }

                _logger.LogWarning("Empty or invalid fingerprint template provided");
                return fingerTemplate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing fingerprint template");
                return fingerTemplate;
            }
        }

        /// <summary>
        /// Check if a string is Base64 encoded
        /// </summary>
        private bool IsBase64String(string s)
        {
            try
            {
                if (string.IsNullOrEmpty(s) || s.Length % 4 != 0)
                    return false;

                Convert.FromBase64String(s);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Security helper for XML operations
    /// </summary>
    public static class SecurityHelper
    {
        public static string EscapeXml(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            return input
                .Replace("&", "&amp;")
                .Replace("<", "&lt;")
                .Replace(">", "&gt;")
                .Replace("\"", "&quot;")
                .Replace("'", "&apos;");
        }

    }

}
