﻿@if (!string.IsNullOrEmpty(Message))
{
    var classOnAlert = "d-flex items-center p-4 mb-3 text-sm gap-2 rounded-lg alert alert-success";
    @if (IsError)
    {
        classOnAlert = "d-flex items-center p-4 mb-3 text-sm gap-2 text-red-800 rounded-lg alert alert-danger";
    }
    <div class="mt-3 @classOnAlert" role="alert">
        <svg width="24" height="24" class="flex-shrink-0 inline mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
        </svg>
        <div>
            @((MarkupString)Message)
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@code {
    [Parameter]
    public bool IsError { get; set; } = false;

    [Parameter]
    public string? Message { get; set; }

}
