using PLRA.ESORS.Framework;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.Portal.ServiceContracts.Features.PartiesInfo;
using Microsoft.EntityFrameworkCore;
using PLRA.ESORS.Portal.ServiceContracts.Models;
using PLRA.ESORS.Server.Data.Entities;
using System.Text.Json;
using System.Net;
using PLRA.ESORS.Framework.Enums;
using System;
namespace PLRA.ESORS.Portal.Server.DataServices.Features.PartiesInfo;
public class PartiesInfoServerSideFormDataService : IPartiesInfoFormDataService
{

    private readonly ApplicationDbContext _context;

    public PartiesInfoServerSideFormDataService(ApplicationDbContext context)
    {
        _context = context;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<long> SaveAsync(PartiesInfoFormBusinessObject formBusinessObject)
    {
        if (formBusinessObject.EStampMainId == 0)
            throw new Exception("Invalid Task Id");

        var party = await _context.Parties.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Appealent_Id && x.EStampMainId == formBusinessObject.EStampMainId && x.Party_Type == formBusinessObject.PARTY_TYPE);

        if (party == null)
        {
            party = new Parties
            {
                Id = _context.GetNextValueOfSequence(ApplicationDbContext.PartiesSequence),
                EStampMainId = formBusinessObject.EStampMainId,
                Address = formBusinessObject.Appealent_Address,
                Email = formBusinessObject.Appealent_Email,
                Name = formBusinessObject.Appealent_Name,
                Relation = formBusinessObject.Appealent_Relation,
                RelativeName = formBusinessObject.Appealent_RelativeName
            };
            _context.Parties.Add(party);
        }

        party.Address = formBusinessObject.Appealent_Address;
        party.Email = formBusinessObject.Appealent_Email;
        party.Name = formBusinessObject.Appealent_Name;
        party.Relation = formBusinessObject.Appealent_Relation;
        party.RelativeName = formBusinessObject.Appealent_RelativeName;
        party.CNIC = formBusinessObject.Appealent_CNIC;
        party.PhoneNumber = formBusinessObject.Appealent_PhoneNumber;
        // Store ApiFieldMetadata in LawyerName field for now (can be moved to dedicated field later)
        party.LawyerName = formBusinessObject.ApiFieldMetadata ?? formBusinessObject.Appealent_LawyerName;
        party.Party_Type = formBusinessObject.PARTY_TYPE;

        await _context.SaveChangesAsync();
        return party.Id;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<PartiesInfoFormBusinessObject?> GetItemByIdAsync(long id)
    {
        var compositeBase = 1_000_000_000_000;
        var flagBit = 1L << 48;
        bool isRowId = (id & flagBit) != 0;
        long cleanId = id & ~flagBit;
        var enumValue = (PARTY_TYPE)(cleanId / compositeBase);
        var recordId = cleanId % compositeBase;
       

        PartiesInfoFormBusinessObject? result = null;
        if (!isRowId)
        {
            result = await _context.Parties.Where(x => x.EStampMainId == recordId && x.Party_Type == enumValue)
                .Select(x => new PartiesInfoFormBusinessObject
                {
                    Appealent_Id = x.Id,
                    EStampMainId = x.EStampMainId,
                    Appealent_Address = x.Address,
                    Appealent_CNIC = x.CNIC.GetValueOrDefault(),
                    Appealent_Email = x.Email,
                    Appealent_LawyerName = x.LawyerName,
                    Appealent_Name = x.Name,
                    Appealent_PhoneNumber = x.PhoneNumber.GetValueOrDefault(),
                    Appealent_Relation = x.Relation,
                    Appealent_RelativeName = x.RelativeName

                }).FirstOrDefaultAsync();
        }
        else
        {
            result = await _context.Parties.Where(x => x.Id == recordId && x.Party_Type == enumValue)
                .Select(x => new PartiesInfoFormBusinessObject
                {
                    Appealent_Id = x.Id,
                    EStampMainId = x.EStampMainId,
                    Appealent_Address = x.Address,
                    Appealent_CNIC = x.CNIC.GetValueOrDefault(),
                    Appealent_Email = x.Email,
                    Appealent_LawyerName = x.LawyerName,
                    Appealent_Name = x.Name,
                    Appealent_PhoneNumber = x.PhoneNumber.GetValueOrDefault(),
                    Appealent_Relation = x.Relation,
                    Appealent_RelativeName = x.RelativeName

                }).FirstOrDefaultAsync();
        }

        return result ?? new PartiesInfoFormBusinessObject();

    }
}
