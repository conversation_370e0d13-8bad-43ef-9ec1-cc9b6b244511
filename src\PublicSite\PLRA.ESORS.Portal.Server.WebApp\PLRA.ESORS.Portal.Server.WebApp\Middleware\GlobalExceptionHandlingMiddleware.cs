using PLRA.ESORS.Portal.ServiceContracts;
using System.Net;
using System.Text.Json;

namespace PLRA.ESORS.Portal.Server.WebApp.Middleware
{
    public class GlobalExceptionHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionHandlingMiddleware> _logger;

        public GlobalExceptionHandlingMiddleware(RequestDelegate next, ILogger<GlobalExceptionHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            // Log the error using our error logging service
            try
            {
                var errorLoggingService = context.RequestServices.GetService<IErrorLoggingService>();
                if (errorLoggingService != null)
                {
                    await errorLoggingService.LogCriticalAsync(
                        "GlobalExceptionMiddleware",
                        "HandleExceptionAsync",
                        exception,
                        new { RequestPath = context.Request.Path, RequestMethod = context.Request.Method }
                    );
                }
            }
            catch (Exception loggingEx)
            {
                _logger.LogError(loggingEx, "Failed to log exception to database");
            }

            // Set response details
            context.Response.ContentType = "application/json";
            
            var response = new
            {
                error = new
                {
                    message = "An internal server error occurred",
                    details = context.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true 
                        ? exception.Message 
                        : "Please contact support if the problem persists",
                    traceId = context.TraceIdentifier
                }
            };

            // Determine status code based on exception type
            context.Response.StatusCode = exception switch
            {
                ArgumentException => (int)HttpStatusCode.BadRequest,
                UnauthorizedAccessException => (int)HttpStatusCode.Unauthorized,
                NotImplementedException => (int)HttpStatusCode.NotImplemented,
                TimeoutException => (int)HttpStatusCode.RequestTimeout,
                _ => (int)HttpStatusCode.InternalServerError
            };

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }
    }

    public static class GlobalExceptionHandlingMiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<GlobalExceptionHandlingMiddleware>();
        }
    }
}
