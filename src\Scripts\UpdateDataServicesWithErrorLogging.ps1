# PowerShell script to inject IErrorLoggingService into all DataService files
# This script systematically updates constructor parameters and adds error logging

$dataServiceFiles = @(
    "src/PLRA.ESORS.Server.DataServices/Features/Courts/Form/CourtDeleteServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/Courts/Listing/CourtServerSideListingDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/ParentCategory/Form/ParentCategoryDeleteServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/ParentCategory/Listing/ParentCategoryServerSideListingDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/SubCategories/Form/SubCategoryDeleteServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/SubCategories/Listing/SubCategoryServerSideListingDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/ThirdLevelCategories/Form/ThirdLevelCategoryServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/ThirdLevelCategories/Form/ThirdLevelCategoryDeleteServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/DeedsManagement/ThirdLevelCategories/Listing/ThirdLevelCategoryServerSideListingDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Form/WorkFlowBuilderServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Form/WorkFlowBuilderDeleteServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Form/WorkFlowStepsServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Form/WorkFlowStepDeleteServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Form/WorkflowVerificationServerSideFormDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Listing/WorkFlowBuilderServerSideListingDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Listing/WorkFlowStepsServerSideListingDataService.cs",
    "src/PLRA.ESORS.Server.DataServices/Features/WorkflowManagement/Listing/WorkflowVerificationServerSideListingDataService.cs"
)

function Add-ErrorLoggingToDataService {
    param(
        [string]$FilePath
    )
    
    Write-Host "Processing: $FilePath"
    
    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return
    }
    
    $content = Get-Content $FilePath -Raw
    
    # Add using statement if not present
    if ($content -notmatch "using PLRA\.ESORS\.ServiceContracts;") {
        $content = $content -replace "(using [^;]+;)", "`$1`nusing PLRA.ESORS.ServiceContracts;"
    }
    
    # Add private field for error logging service
    if ($content -notmatch "private readonly IErrorLoggingService _errorLoggingService;") {
        $content = $content -replace "(private readonly ApplicationDbContext _context;)", "`$1`n    private readonly IErrorLoggingService _errorLoggingService;"
    }
    
    # Update constructor to include IErrorLoggingService parameter
    $content = $content -replace "(\w+)\(([^)]*ApplicationDbContext context[^)]*)\)", "`$1(`$2, IErrorLoggingService errorLoggingService)"
    
    # Add assignment in constructor
    if ($content -notmatch "_errorLoggingService = errorLoggingService;") {
        $content = $content -replace "(_context = context;)", "`$1`n        _errorLoggingService = errorLoggingService;"
    }
    
    Set-Content $FilePath $content -Encoding UTF8
    Write-Host "Updated: $FilePath"
}

# Process all DataService files
foreach ($file in $dataServiceFiles) {
    Add-ErrorLoggingToDataService -FilePath $file
}

Write-Host "Completed updating all DataService files with IErrorLoggingService injection."
Write-Host "Next steps:"
Write-Host "1. Add try-catch blocks with error logging to critical methods"
Write-Host "2. Build the project to verify all dependencies are resolved"
Write-Host "3. Test error logging functionality"
