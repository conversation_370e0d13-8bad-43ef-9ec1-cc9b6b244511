using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.Server.Data.Entities;
using PLRA.ESORS.ServiceContracts;
using System.Text.Json;

namespace PLRA.ESORS.Server.DataServices.Services.ErrorLogging
{
    public class ErrorLoggingServerSideDataService : IErrorLoggingService
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticatedUser _authenticatedUser;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ErrorLoggingServerSideDataService(
            ApplicationDbContext context,
            IAuthenticatedUser authenticatedUser,
            IHttpContextAccessor httpContextAccessor)
        {
            _context = context;
            _authenticatedUser = authenticatedUser;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task LogErrorAsync(string componentName, string methodName, Exception exception, object? additionalData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.General, additionalData);
        }

        public async Task LogErrorAsync(string componentName, string methodName, string errorMessage, string? stackTrace = null, object? additionalData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, errorMessage, stackTrace, null, ErrorCategories.General, additionalData);
        }

        public async Task LogWarningAsync(string componentName, string methodName, string message, object? additionalData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Warning, message, null, null, ErrorCategories.General, additionalData);
        }

        public async Task LogInformationAsync(string componentName, string methodName, string message, object? additionalData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Information, message, null, null, ErrorCategories.General, additionalData);
        }

        public async Task LogCriticalAsync(string componentName, string methodName, Exception exception, object? additionalData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Critical, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.General, additionalData);
        }

        public async Task LogCriticalAsync(string componentName, string methodName, string errorMessage, string? stackTrace = null, object? additionalData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Critical, errorMessage, stackTrace, null, ErrorCategories.General, additionalData);
        }

        public async Task LogDatabaseErrorAsync(string componentName, string methodName, Exception exception, string? sqlQuery = null, object? parameters = null)
        {
            var additionalData = new
            {
                SqlQuery = sqlQuery,
                Parameters = parameters
            };
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.Database, additionalData);
        }

        public async Task LogApiErrorAsync(string componentName, string methodName, Exception exception, string? requestUrl = null, string? requestBody = null, string? responseBody = null)
        {
            var additionalData = new
            {
                RequestUrl = requestUrl,
                RequestBody = requestBody,
                ResponseBody = responseBody
            };
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.Api, additionalData);
        }

        public async Task LogValidationErrorAsync(string componentName, string methodName, string validationMessage, object? invalidData = null)
        {
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Warning, validationMessage, null, null, ErrorCategories.Validation, invalidData);
        }

        public async Task LogAuthenticationErrorAsync(string componentName, string methodName, string errorMessage, string? userId = null, string? attemptedAction = null)
        {
            var additionalData = new
            {
                AttemptedUserId = userId,
                AttemptedAction = attemptedAction
            };
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, errorMessage, null, null, ErrorCategories.Authentication, additionalData);
        }

        public async Task LogFileOperationErrorAsync(string componentName, string methodName, Exception exception, string? filePath = null, string? operation = null)
        {
            var additionalData = new
            {
                FilePath = filePath,
                Operation = operation
            };
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.FileOperation, additionalData);
        }

        public async Task LogExternalServiceErrorAsync(string componentName, string methodName, Exception exception, string? serviceName = null, string? serviceEndpoint = null)
        {
            var additionalData = new
            {
                ServiceName = serviceName,
                ServiceEndpoint = serviceEndpoint
            };
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.ExternalService, additionalData);
        }

        public async Task LogWorkflowErrorAsync(string componentName, string methodName, Exception exception, long? taskId = null, long? workflowId = null, string? workflowStep = null)
        {
            var additionalData = new
            {
                WorkflowStep = workflowStep
            };
            await LogErrorInternalAsync(componentName, methodName, ErrorSeverityLevels.Error, exception.Message, exception.StackTrace, exception.InnerException?.ToString(), ErrorCategories.Workflow, additionalData, taskId, workflowId);
        }

        private async Task LogErrorInternalAsync(string componentName, string methodName, string severityLevel, string errorMessage, string? stackTrace, string? innerException, string errorCategory, object? additionalData, long? taskId = null, long? workflowId = null)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                
                var errorLog = new ErrorLog
                {
                    Timestamp = DateTime.UtcNow,
                    UserId = _authenticatedUser?.UserId,
                    UserName = _authenticatedUser?.Username,
                    ComponentName = componentName,
                    MethodName = methodName,
                    SeverityLevel = severityLevel,
                    ErrorMessage = errorMessage,
                    StackTrace = stackTrace,
                    InnerException = innerException,
                    ErrorCategory = errorCategory,
                    TaskId = taskId,
                    WorkflowId = workflowId,
                    RequestPath = httpContext?.Request?.Path,
                    HttpMethod = httpContext?.Request?.Method,
                    UserAgent = httpContext?.Request?.Headers["User-Agent"].FirstOrDefault(),
                    IPAddress = GetClientIPAddress(httpContext),
                    SessionId = httpContext?.Session?.Id,
                    CorrelationId = httpContext?.TraceIdentifier,
                    AdditionalData = additionalData != null ? JsonSerializer.Serialize(additionalData) : null,
                    IsActive = true
                };

                _context.ErrorLogs.Add(errorLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Prevent infinite loop - log to system logger or file if database logging fails
                System.Diagnostics.Debug.WriteLine($"Failed to log error to database: {ex.Message}");
            }
        }

        private string? GetClientIPAddress(HttpContext? httpContext)
        {
            if (httpContext == null) return null;

            var ipAddress = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = httpContext.Connection.RemoteIpAddress?.ToString();
            }

            return ipAddress;
        }

        public async Task<IEnumerable<ErrorLog>> GetErrorLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? severityLevel = null, string? componentName = null, int skip = 0, int take = 100)
        {
            var query = _context.ErrorLogs.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(e => e.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(e => e.Timestamp <= toDate.Value);

            if (!string.IsNullOrEmpty(severityLevel))
                query = query.Where(e => e.SeverityLevel == severityLevel);

            if (!string.IsNullOrEmpty(componentName))
                query = query.Where(e => e.ComponentName.Contains(componentName));

            return await query
                .OrderByDescending(e => e.Timestamp)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }

        public async Task MarkErrorAsResolvedAsync(long errorLogId, string resolutionNotes)
        {
            var errorLog = await _context.ErrorLogs.FindAsync(errorLogId);
            if (errorLog != null)
            {
                errorLog.IsResolved = true;
                errorLog.ResolvedAt = DateTime.UtcNow;
                errorLog.ResolvedBy = _authenticatedUser?.UserId;
                errorLog.ResolutionNotes = resolutionNotes;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<ErrorLogStatistics> GetErrorStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.ErrorLogs.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(e => e.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(e => e.Timestamp <= toDate.Value);

            var statistics = new ErrorLogStatistics
            {
                TotalErrors = await query.CountAsync(),
                CriticalErrors = await query.CountAsync(e => e.SeverityLevel == ErrorSeverityLevels.Critical),
                Warnings = await query.CountAsync(e => e.SeverityLevel == ErrorSeverityLevels.Warning),
                UnresolvedErrors = await query.CountAsync(e => !e.IsResolved),
                ErrorsByComponent = await query.GroupBy(e => e.ComponentName).ToDictionaryAsync(g => g.Key, g => g.Count()),
                ErrorsBySeverity = await query.GroupBy(e => e.SeverityLevel).ToDictionaryAsync(g => g.Key, g => g.Count()),
                ErrorsByDate = await query.GroupBy(e => e.Timestamp.Date).ToDictionaryAsync(g => g.Key, g => g.Count())
            };

            return statistics;
        }
    }
}
