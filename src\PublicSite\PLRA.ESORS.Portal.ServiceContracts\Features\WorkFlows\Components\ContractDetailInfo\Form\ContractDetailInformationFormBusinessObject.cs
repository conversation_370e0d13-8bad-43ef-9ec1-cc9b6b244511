﻿using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
namespace PLRA.ESORS.Portal.ServiceContracts.Features.ContractDetailInformation;
public class ContractDetailInformationFormBusinessObject
{
    public long? Id { get; set; }

    public long EStampMainId { get; set; }
    public CONTRACT_TYPE CONTRACT_TYPE { get; set; }
    public string? Title { get; set; }
    public string? Type { get; set; }
    public int Scope { get; set; }
    public int Amount { get; set; }
    public string? Duration { get; set; }
    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public string? Terms { get; set; }
    public int DistrictId { get; set; }
    public int TehsilId { get; set; }

    public CONTRACT_CLAUSES CONTRACT_CLAUSES { get; set; }
}
