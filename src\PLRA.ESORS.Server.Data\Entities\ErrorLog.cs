using PLRA.ESORS.Server.Data.Data;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PLRA.ESORS.Server.Data.Entities
{
    public class ErrorLog : BaseEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Required]
        public DateTime Timestamp { get; set; }

        [StringLength(36)]
        public string? UserId { get; set; }

        [StringLength(256)]
        public string? UserName { get; set; }

        [Required]
        [StringLength(100)]
        public string ComponentName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string MethodName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string SeverityLevel { get; set; } = string.Empty;

        [Required]
        public string ErrorMessage { get; set; } = string.Empty;

        public string? StackTrace { get; set; }

        public string? InnerException { get; set; }

        [StringLength(500)]
        public string? RequestPath { get; set; }

        [StringLength(1000)]
        public string? UserAgent { get; set; }

        [StringLength(45)]
        public string? IPAddress { get; set; }

        [StringLength(50)]
        public string? HttpMethod { get; set; }

        public int? StatusCode { get; set; }

        public string? AdditionalData { get; set; }

        [StringLength(100)]
        public string? CorrelationId { get; set; }

        [StringLength(100)]
        public string? SessionId { get; set; }

        public long? TaskId { get; set; }

        public long? WorkflowId { get; set; }

        [StringLength(50)]
        public string? ErrorCategory { get; set; }

        public bool IsResolved { get; set; } = false;

        public DateTime? ResolvedAt { get; set; }

        [StringLength(36)]
        public string? ResolvedBy { get; set; }

        public string? ResolutionNotes { get; set; }
    }
}
