using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Portal.ServiceContracts.SelectList;

namespace PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo.Form
{
    public partial class PartiesInfoAttorneyOptionalFormComponent
    {
        [Parameter]
        public long TaskId { get; set; }

        [Parameter]
        public int Step { get; set; }

        [Parameter]
        public PARTY_TYPE PARTY_TYPE { get; set; }

        [Parameter, EditorRequired]
        public string? Heading_En { get; set; }

        [Parameter, EditorRequired]
        public string? Heading_Ur { get; set; }

        [Parameter, EditorRequired]
        public string FormName { get; set; } = string.Empty;

        [Parameter]
        public bool FormHasListing { get; set; } = false;

        [SupplyParameterFromQuery(Name = "rowId")]
        public long RowId { get; set; } = 0;
        
        [SupplyParameterFromQuery(Name = "showAttorny")]
        public bool ShowAttorny { get; set; }

        private string? _message;

        [SupplyParameterFromQuery(Name = "cnic")]
        public long CNIC { get; set; } = 0;

        [SupplyParameterFromQuery(Name = "partyType")]
        public string SELECTED_PARTY_TYPE { get; set; } = string.Empty;

        // Properties to track which fields are populated from API and should be disabled
        private readonly Dictionary<string, bool> _fieldsFromApi = new()
        {
            ["name"] = false,
            ["relation"] = false,
            ["relativename"] = false,
            ["address"] = false,
            ["phonenumber"] = false,
            ["email"] = false,
            ["cnic"] = false
        };

        // Store original values to detect API vs user changes
        private readonly Dictionary<string, string?> _originalApiValues = new();

        // Property to track if CNIC search has been performed
        private bool _hasPerformedCnicSearch = false;

        // Track the CNIC that was successfully used to populate data from API
        private long? _lastSuccessfulCnic = null;

        // Track the current state of API data retrieval
        private enum ApiDataState
        {
            NoApiCallMade,      // No API call has been made yet
            ApiCallSuccessful,  // API call was successful and data was populated
            ApiCallFailed       // API call was made but failed (no data or error)
        }
        private ApiDataState _currentApiState = ApiDataState.NoApiCallMade;

        protected override async Task OnInitializedAsync()
        {
            Id = (long)PARTY_TYPE * 1_000_000_000_000 + (FormHasListing ? RowId : TaskId);
            var flagBit = 1L << 48;        // bit at position 48 only concat when need to fetch record with Id
            if (FormHasListing)
                Id |= flagBit;

            await base.OnInitializedAsync();

            if (SelectedItem != null)
            {
                ShowAttorny = ShowAttorny || SelectedItem.ShowAttorny.GetValueOrDefault();
                SelectedItem.PARTY_TYPE = PARTY_TYPE;
                SelectedItem.Attorney_Id = SelectedItem.Attorney_Id ?? (FormHasListing ? RowId : 0);
                SelectedItem.EStampMainId = TaskId;

                // Handle CNIC search from query parameter (triggered by JavaScript redirect)
                if (CNIC > 0 && (!string.IsNullOrEmpty(SELECTED_PARTY_TYPE) &&
                    (PARTY_TYPE)Enum.Parse(typeof(PARTY_TYPE), SELECTED_PARTY_TYPE) == PARTY_TYPE))
                {
                    await PerformCnicSearchFromQuery(CNIC);
                }
                else if (SelectedItem.Attorney_CNIC != null && SelectedItem.Attorney_CNIC > 0)
                {
                    // When loading existing data, determine which fields might have come from API
                    await DetermineFieldSourcesFromExistingData();
                }
            }
        }

        public override async Task OnAfterSaveAsync(long key)
        {
            ArgumentNullException.ThrowIfNull(SelectedItem);

            string url = string.Empty;
            if (key > 0)
            {
                SelectedItem.Attorney_Id = key;
                _message = "Record has been saved successfully";
                url = NavigationManager?.AddQueryString(new { partyType = PARTY_TYPE })!;

            }
            if (FormHasListing)
            {

                SelectedItem = await CreateSelectedItem();
                url = NavigationManager?.AddQueryString(new { reloadparty = true, partyType = PARTY_TYPE })!;
            }

            NavigationManager?.NavigateTo(url);
        }

        /// <summary>
        /// Performs CNIC search triggered by query parameter from JavaScript redirect
        /// </summary>
        /// <param name="cnicToSearch">CNIC number to search</param>
        private async Task PerformCnicSearchFromQuery(long cnicToSearch)
        {
            try
            {
                if (cnicToSearch <= 0 || SelectedItem == null)
                {
                    ValidationError = "Please enter a valid CNIC number";
                    return;
                }

                // Check if this is a different CNIC than the previously successful one
                bool isCnicChanged = _lastSuccessfulCnic.HasValue && _lastSuccessfulCnic.Value != cnicToSearch;

                using var scope = ScopeFactory?.CreateScope();
                var service = scope?.ServiceProvider.GetRequiredService<ISelectListDataService>();
                ArgumentNullException.ThrowIfNull(service);

                var person = await service.GetPersonDetail(cnicToSearch);
                if (person != null)
                {
                    // If CNIC changed and we had previous API data, clear it first
                    if (isCnicChanged && _currentApiState == ApiDataState.ApiCallSuccessful)
                    {
                        ClearApiPopulatedFields();
                    }

                    // Clear any previous validation errors
                    ValidationError = null;
                    _message = "Record found and data populated from API";

                    // Bind data from API and track which fields are populated
                    await PopulateFieldsFromApiData(person);

                    _hasPerformedCnicSearch = true;
                    _lastSuccessfulCnic = cnicToSearch;
                    _currentApiState = ApiDataState.ApiCallSuccessful;
                }
                else
                {
                    // API call failed - handle based on whether CNIC changed
                    await HandleApiCallFailure(cnicToSearch, isCnicChanged);
                }
            }
            catch (Exception ex)
            {
                // Exception occurred - handle based on whether CNIC changed
                bool isCnicChanged = _lastSuccessfulCnic.HasValue && _lastSuccessfulCnic.Value != cnicToSearch;
                await HandleApiCallFailure(cnicToSearch, isCnicChanged, ex);
            }
        }

        /// <summary>
        /// Populates form fields from API data and tracks which fields were populated
        /// </summary>
        /// <param name="person">Person data from API</param>
        private async Task PopulateFieldsFromApiData(dynamic person)
        {
            // Only populate fields that are currently empty to avoid overwriting user data
            if (!string.IsNullOrEmpty(person.CNIC) && (SelectedItem.Attorney_CNIC == null || SelectedItem.Attorney_CNIC == 0))
            {
                SelectedItem.Attorney_CNIC = Convert.ToInt64(person.CNIC);
                _fieldsFromApi["cnic"] = true;
                _originalApiValues["cnic"] = person.CNIC;
            }

            if (!string.IsNullOrEmpty(person.FirstName) && string.IsNullOrEmpty(SelectedItem.Attorney_Name))
            {
                SelectedItem.Attorney_Name = person.FirstName;
                _fieldsFromApi["name"] = true;
                _originalApiValues["name"] = person.FirstName;
            }

            if (!string.IsNullOrEmpty(person.Relation) && string.IsNullOrEmpty(SelectedItem.Attorney_Relation))
            {
                SelectedItem.Attorney_Relation = person.Relation;
                _fieldsFromApi["relation"] = true;
                _originalApiValues["relation"] = person.Relation;
            }

            if (!string.IsNullOrEmpty(person.RelativeName) && string.IsNullOrEmpty(SelectedItem.Attorney_RelativeName))
            {
                SelectedItem.Attorney_RelativeName = person.RelativeName;
                _fieldsFromApi["relativename"] = true;
                _originalApiValues["relativename"] = person.RelativeName;
            }

            if (!string.IsNullOrEmpty(person.Address) && string.IsNullOrEmpty(SelectedItem.Attorney_Address))
            {
                SelectedItem.Attorney_Address = person.Address;
                _fieldsFromApi["address"] = true;
                _originalApiValues["address"] = person.Address;
            }

            if (!string.IsNullOrEmpty(person.Phone) && (SelectedItem.Attorney_PhoneNumber == null || SelectedItem.Attorney_PhoneNumber == 0))
            {
                if (long.TryParse(person.Phone, out long phoneNumber))
                {
                    SelectedItem.Attorney_PhoneNumber = phoneNumber;
                    _fieldsFromApi["phonenumber"] = true;
                    _originalApiValues["phonenumber"] = person.Phone;
                }
            }

            if (!string.IsNullOrEmpty(person.Email) && string.IsNullOrEmpty(SelectedItem.Attorney_Email))
            {
                SelectedItem.Attorney_Email = person.Email;
                _fieldsFromApi["email"] = true;
                _originalApiValues["email"] = person.Email;
            }
        }

        /// <summary>
        /// Determines which fields might have come from API based on existing data
        /// This is used when loading existing records to maintain field states
        /// </summary>
        private async Task DetermineFieldSourcesFromExistingData()
        {
            if (SelectedItem == null || SelectedItem.Attorney_CNIC == null || SelectedItem.Attorney_CNIC <= 0)
                return;

            try
            {
                // Check if the current data matches API data for this CNIC
                using var scope = ScopeFactory?.CreateScope();
                var service = scope?.ServiceProvider.GetRequiredService<ISelectListDataService>();
                ArgumentNullException.ThrowIfNull(service);

                var person = await service.GetPersonDetail(SelectedItem.Attorney_CNIC.Value);
                if (person != null)
                {
                    // Compare current values with API values to determine which fields came from API
                    if (!string.IsNullOrEmpty(person.FirstName) &&
                        string.Equals(SelectedItem.Attorney_Name, person.FirstName, StringComparison.OrdinalIgnoreCase))
                    {
                        _fieldsFromApi["name"] = true;
                        _originalApiValues["name"] = person.FirstName;
                    }

                    if (!string.IsNullOrEmpty(person.Relation) &&
                        string.Equals(SelectedItem.Attorney_Relation, person.Relation, StringComparison.OrdinalIgnoreCase))
                    {
                        _fieldsFromApi["relation"] = true;
                        _originalApiValues["relation"] = person.Relation;
                    }

                    if (!string.IsNullOrEmpty(person.RelativeName) &&
                        string.Equals(SelectedItem.Attorney_RelativeName, person.RelativeName, StringComparison.OrdinalIgnoreCase))
                    {
                        _fieldsFromApi["relativename"] = true;
                        _originalApiValues["relativename"] = person.RelativeName;
                    }

                    if (!string.IsNullOrEmpty(person.Address) &&
                        string.Equals(SelectedItem.Attorney_Address, person.Address, StringComparison.OrdinalIgnoreCase))
                    {
                        _fieldsFromApi["address"] = true;
                        _originalApiValues["address"] = person.Address;
                    }

                    if (!string.IsNullOrEmpty(person.Phone) &&
                        long.TryParse(person.Phone, out var phoneNumber) &&
                        SelectedItem.Attorney_PhoneNumber == phoneNumber)
                    {
                        _fieldsFromApi["phonenumber"] = true;
                        _originalApiValues["phonenumber"] = person.Phone;
                    }

                    if (!string.IsNullOrEmpty(person.Email) &&
                        string.Equals(SelectedItem.Attorney_Email, person.Email, StringComparison.OrdinalIgnoreCase))
                    {
                        _fieldsFromApi["email"] = true;
                        _originalApiValues["email"] = person.Email;
                    }

                    // If any fields match API data, consider CNIC search as performed
                    if (_fieldsFromApi.Values.Any(x => x))
                    {
                        _hasPerformedCnicSearch = true;
                        _fieldsFromApi["cnic"] = true;
                        _originalApiValues["cnic"] = person.CNIC;

                        // Set state variables for existing data
                        _lastSuccessfulCnic = SelectedItem.Attorney_CNIC;
                        _currentApiState = ApiDataState.ApiCallSuccessful;
                    }
                }
            }
            catch (Exception)
            {
                // If API call fails, don't mark any fields as from API
                // This ensures fields remain editable
            }
        }

        /// <summary>
        /// Checks if a field should be disabled based on whether it was populated from API
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <returns>True if field should be disabled</returns>
        public bool IsFieldDisabled(string fieldName)
        {
            if (!_hasPerformedCnicSearch) return false;

            var key = fieldName.ToLower();
            return _fieldsFromApi.ContainsKey(key) && _fieldsFromApi[key];
        }

        /// <summary>
        /// Gets CSS class for disabled fields
        /// </summary>
        /// <param name="fieldName">Name of the field</param>
        /// <returns>CSS class string</returns>
        public string GetFieldCssClass(string fieldName)
        {
            var baseClass = "form-control";
            if (fieldName == "name" || fieldName == "relation" || fieldName == "relativename" || fieldName == "address")
            {
                baseClass += " urdu-text";
            }

            if (IsFieldDisabled(fieldName))
            {
                baseClass += " bg-light text-muted";
            }

            return baseClass;
        }

        /// <summary>
        /// Checks if a field value has been modified from its original API value
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <param name="currentValue">Current field value</param>
        /// <returns>True if field has been modified from API value</returns>
        public bool IsFieldModifiedFromApi(string fieldName, string? currentValue)
        {
            var key = fieldName.ToLower();
            if (!_fieldsFromApi.ContainsKey(key) || !_fieldsFromApi[key])
                return false;

            if (!_originalApiValues.ContainsKey(key))
                return false;

            return !string.Equals(currentValue, _originalApiValues[key], StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets a message indicating the source of field data
        /// </summary>
        /// <param name="fieldName">Name of the field</param>
        /// <returns>Message string or null</returns>
        public string? GetFieldSourceMessage(string fieldName)
        {
            if (IsFieldDisabled(fieldName))
            {
                return "This field was populated from API and is disabled";
            }
            return null;
        }

        /// <summary>
        /// Clears all fields that were previously populated by API data
        /// </summary>
        private void ClearApiPopulatedFields()
        {
            if (SelectedItem == null) return;

            // Clear fields that were populated from API
            foreach (var field in _fieldsFromApi.Where(f => f.Value).ToList())
            {
                switch (field.Key)
                {
                    case "name":
                        SelectedItem.Attorney_Name = null;
                        break;
                    case "relation":
                        SelectedItem.Attorney_Relation = null;
                        break;
                    case "relativename":
                        SelectedItem.Attorney_RelativeName = null;
                        break;
                    case "address":
                        SelectedItem.Attorney_Address = null;
                        break;
                    case "phonenumber":
                        SelectedItem.Attorney_PhoneNumber = null;
                        break;
                    case "email":
                        SelectedItem.Attorney_Email = null;
                        break;
                    case "cnic":
                        // Don't clear CNIC as user is entering a new one
                        break;
                }

                // Reset field tracking
                _fieldsFromApi[field.Key] = false;
            }

            // Clear original API values
            _originalApiValues.Clear();

            // Reset state flags
            _hasPerformedCnicSearch = false;
            _currentApiState = ApiDataState.NoApiCallMade;
            _lastSuccessfulCnic = null;
        }

        /// <summary>
        /// Handles API call failure scenarios based on whether CNIC changed
        /// </summary>
        /// <param name="cnicToSearch">The CNIC that was searched</param>
        /// <param name="isCnicChanged">Whether the CNIC is different from the previously successful one</param>
        /// <param name="exception">Optional exception that occurred</param>
        private async Task HandleApiCallFailure(long cnicToSearch, bool isCnicChanged, Exception? exception = null)
        {
            // If CNIC changed and we had previous successful API data, clear the old data
            if (isCnicChanged && _currentApiState == ApiDataState.ApiCallSuccessful)
            {
                ClearApiPopulatedFields();
            }

            // Set error message
            if (exception != null)
            {
                ValidationError = $"Error searching CNIC: {exception.Message}";
            }
            else
            {
                ValidationError = $"No record found for CNIC: {cnicToSearch}";
            }

            _message = null;
            _currentApiState = ApiDataState.ApiCallFailed;

            // Don't update _lastSuccessfulCnic since this call failed
            // This ensures we can detect future CNIC changes correctly

            await Task.CompletedTask; // For async consistency
        }
    }
}