using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Server.Data.Entities;

namespace PLRA.ESORS.Server.Data.Data;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IAuthenticatedUser authenticatedUserId) : IdentityDbContext<ApplicationUser>(options)
{
    private readonly IAuthenticatedUser? _authenticatedUserId = authenticatedUserId;

    public DbSet<AssignedTasks> AssignedTasks { get; set; }
    public DbSet<Court> Courts { get; set; }
    public DbSet<UserProfile> Profiles { get; set; }
    public DbSet<Purpose> Purposes { get; set; }
    public DbSet<WorkFlow> WorkFlows { get; set; }
    public DbSet<WorkFlowStep> WorkFlowSteps { get; set; }
    public DbSet<WorkFlowVerificationStep> WorkFlowVerificationSteps { get; set; }
    public DbSet<WorkflowExecutionLog> WorkflowExecutionLogs { get; set; }
    public DbSet<EStampMain> EStampMain { get; set; }
    public DbSet<CourtFee> CourtFees { get; set; }
    public DbSet<BiometricVerification> BiometricVerifications { get; set; }
    public DbSet<ErrorLog> ErrorLogs { get; set; }
    public DbSet<Agreement> Agreements { get; set; }
    public DbSet<Parties> Parties { get; set; }
    public DbSet<StampInfo> StampInfo { get; set; }
    public DbSet<BusinessContract> BusinessContracts { get; set; }
    public DbSet<GeneralPowerOfAttorneyInfo> GeneralPowerOfAttorneyInfo { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<PaymentDetail> PaymentDetails { get; set; }
    public DbSet<PropertyPowerOfAttorneyInfo> PropertyPowerOfAttorneyInfo { get; set; }
    public DbSet<Resource> Resources { get; set; }
    public DbSet<AgreementMemorandum> AgreementMemorandums { get; set; }



    public DbSet<ArticleOfClerkship> ArticleOfClerkships { get; set; }
    public DbSet<Mortgage> Mortgages { get; set; }
    public DbSet<Partnership> Partnerships { get; set; }
    public DbSet<Lease> Leases { get; set; }

    #region Property Tables
    public DbSet<Conveyance23> Conveyance23s { get; set; }
    public DbSet<Conveyance23RuralAndUrban> Conveyance23RuralAndUrbans { get; set; }

    public DbSet<CertificateOfSale18> CertificateOfSale18s { get; set; }
    public DbSet<CertificateOfSale18RuralAndUrban> CertificateOfSale18RuralAndUrbans { get; set; }

    public DbSet<Gift33> Gift33s { get; set; }
    public DbSet<Gift33RuralAndUrban> Gift33RuralAndUrbans { get; set; }


    public DbSet<PowerOfAttorney48b> PowerOfAttorney48bs { get; set; }
    public DbSet<PowerOfAttorney48bRuralAndUrban> PowerOfAttorney48bRuralAndUrbans { get; set; }

    public DbSet<Decree27A> Decree27As { get; set; }
    public DbSet<Decree27ARuralAndUrban> Decree27ARuralAndUrbans { get; set; }

    public DbSet<AgreementMemorandum5CC> AgreementMemorandum5CCs { get; set; }
    public DbSet<AgreementMemorandum5CCRuralAndUrban> Memorandum5CCRuralAndUrbans { get; set; }

    public DbSet<Lease352A> Lease352As { get; set; }
    public DbSet<Lease352ARuralAndUrban> Lease352ARuralAndUrban { get; set; }

    public DbSet<Lease351B> Lease351Bs { get; set; }
    public DbSet<Lease351BRuralAndUrban> Lease351BRuralAndUrbans { get; set; }

    public DbSet<Lease351C1D> Lease351C1Ds { get; set; }
    public DbSet<Lease351C1DRuralAndUrban> Lease351C1DRuralAndUrbans { get; set; }

    public DbSet<ExchangeOfImmovableProperty31> ExchangeOfImmovableProperty31s { get; set; }
    public DbSet<ExchangeOfImmovableProperty31RuralAndUrban> ExchangeOfImmovableProperty31RuralAndUrbans { get; set; }

    public DbSet<LeaseTransfer63> LeaseTransfer63s { get; set; }
    public DbSet<LeaseTransfer63RuralAndUrban> LeaseTransfer63RuralAndUrbans { get; set; }

    public DbSet<Release55A> Release55As { get; set; }
    public DbSet<Release55ARuralAndUrban> Release55ARuralAndUrbans { get; set; }


    #endregion

    public DbSet<Transfer> Transfers { get; set; }
    public DbSet<BillOfExchange> BillOfExchanges { get; set; }
    public DbSet<BillOfExchange13A2> BillOfExchanges13A2 { get; set; }
    public DbSet<BillOfExchange13B1> BillOfExchanges13B1 { get; set; }
    public DbSet<BillOfExchange13B2> BillOfExchanges13B2 { get; set; }
    public DbSet<BillOfExchange13B3> BillOfExchanges13B3 { get; set; }
    public DbSet<Award> Awards { get; set; }
    public DbSet<Partition> Partitions { get; set; }
    public DbSet<Bond> Bonds { get; set; }
    public DbSet<Cancellation> Cancellations { get; set; }
    public DbSet<BottomryBond> BottomryBonds { get; set; }
    public DbSet<AuthenticatedDeclaration> AuthenticatedDeclarations { get; set; }
    public DbSet<FurtherCharge> FurtherCharges { get; set; }
    public DbSet<PromissoryNote> PromissoryNotes { get; set; }
    public DbSet<ShareWarrent59> ShareWarrent59s { get; set; }
    public DbSet<NoteOrMemorandum> NoteOrMemorandums { get; set; }
    public DbSet<DebentureOrParticipationTerm> DebentureOrParticipationTerms { get; set; }
    public DbSet<Divorce29> Divorce29s { get; set; }
    public DbSet<MortgageForHousingFinanceiv> MortgageForHousingFinanceivs { get; set; }
    public DbSet<Rectification159> Rectification159s { get; set; }
    public DbSet<SurrenderOfLease> SurrenderOfLeases { get; set; }
    public DbSet<Settlement> Settlements { get; set; }
    public DbSet<PropertyDetail> PropertyDetails { get; set; }
    public DbSet<RespondentiaBond56> RespondentiaBond56s { get; set; }
    public DbSet<DeedDepositAgreement> DeedDepositAgreements { get; set; }
    public DbSet<Transfer62b> Transfer62bs { get; set; }

    public const string AssignedTaskSequence = "AssignedTaskSequence";
    public const string PropertyDetailSequence = "PropertyDetailSequence";
    public const string CourtSequence = "CourtSequence";
    public const string PurposeSequence = "PurposeSequence";
    public const string WorkFlowSequence = "WorkFlowSequence";
    public const string WorkFlowStepSequence = "WorkFlowStepSequence";
    public const string WorkFlowVerificationStepSequence = "WorkFlowVerificationStepSequence";
    public const string WorkFlowExecutionLogSequence = "WorkFlowExecutionLogSequence";
    public const string EStampMainSequence = "EStampMainSequence";
    public const string CourtFeeSequence = "CourtFeeSequence";
    public const string BiometricSequence = "BiometricSequence";
    public const string ErrorLogSequence = "ErrorLogSequence";
    public const string AgreementSequence = "AgreementSequence";
    public const string PartiesSequence = "PartiesSequence";
    public const string StampInfoSequence = "StampInfoSequence";
    public const string BusinessContractSequence = "BusinessContractSequence";
    public const string GeneralPowerOfAttorneyInfoSequence = "GeneralPowerOfAttorneyInfoSequence";
    public const string PaymentSequence = "PaymentSequence";
    public const string PaymentDetailSequence = "PaymentDetailSequence";
    public const string PropertyPowerOfAttorneyInfoSequence = "PropertyPowerOfAttorneyInfoSequence";
    public const string ResourceSequence = "ResourceSequence";
    public const string ArticleOfClerkshipSequence = "ArticleOfClerkshipSequence";
    public const string AgreementMemorandumSequence = "AgreementMemorandumSequence";
    public const string MortgageSequence = "MortgageSequence";
    public const string PartnershipSequence = "PartnershipSequence";
    public const string LeaseSequence = "LeaseSequence";
    public const string Conveyance23Sequence = "Conveyance23Sequence";
    public const string Conveyance23RuralAndUrbansSequence = "Conveyance23RuralAndUrbansSequence";
    public const string TransferSequence = "TransferSequence";
    public const string BillofExchangeSequence = "BillofExchangeSequence";
    public const string BillofExchange13A2Sequence = "BillofExchange13A2Sequence";
    public const string BillofExchange13B1Sequence = "BillofExchange13B1Sequence";
    public const string BillofExchange13B2Sequence = "BillofExchange13B2Sequence";
    public const string BillofExchange13B3Sequence = "BillofExchange13B3Sequence";
    public const string AwardSequence = "AwardSequence";
    public const string PartitionSequence = "PartitionSequence";
    public const string BondSequence = "BondSequence";
    public const string CancellationSequence = "CancellationSequence";
    public const string BottomryBondSequence = "BottomryBondSequence";
    public const string AuthenticatedDeclarationSequence = "AuthenticatedDeclarationSequence";

    public const string Memorandum5ccSequence = "Memorandum5ccSequence";
    public const string Memorandum5ccRuralAndUrbanSequence = "Memorandum5ccRuralAndUrbanSequence";

    public const string Gift33Sequence = "Gift33Sequence";
    public const string Gift33RuralAndUrbanSequence = "Gift33RuralAndUrbanSequence";

    public const string CertificateOfSale18Sequence = "CertificateOfSale18Sequence";
    public const string CertificateOfSale18RuralAndUrbanSequence = "CertificateOfSale18RuralAndUrbanSequence";

    public const string POA48bSequence = "POA48bSequence";
    public const string POA48bRuralAndUrbanSequence = "POA48bRuralAndUrbanSequence";

    public const string Decree27ASequence = "Decree27ASequence";
    public const string Decree27ARuralAndUrbanSequence = "Decree27ARuralAndUrbanSequence";
    public const string FurtherChargeSequence = "FurtherChargeSequence";
    public const string ShareWarrent59Sequence = "ShareWarrent59Sequence";

    public const string Lease352ASequence = "Lease352ASequence";
    public const string Lease352ARuralAndUrbanSequence = "Lease352ARuralAndUrbanSequence";

    public const string Lease351BSequence = "Lease351BSequence";
    public const string Lease351BRuralAndUrbanSequence = "Lease351BRuralAndUrbanSequence";

    public const string Lease351C1DSequence = "Lease351C1DSequence";
    public const string Lease351C1DRuralAndUrbanSequence = "Lease351C1DRuralAndUrbanSequence";

    public const string NoteOrMemorandumSequence = "NoteOrMemorandumSequence";

    public const string ExchangeOfImmovableProperty31Sequence = "ExchangeOfImmovableProperty31Sequence";
    public const string ExchangeOfImmovableProperty31RuralAndUrbanSequence = "ExchangeOfImmovableProperty31RuralAndUrbanSequence";
    public const string PromissoryNoteSequence = "PromissoryNoteSequence";
    public const string DebentureOrParticipationTermSequence = "DebentureOrParticipationTermSequence";
    public const string Divorce29Sequence = "Divorce29Sequence";
    public const string MortgageForHousingFinanceivSequence = "MortgageForHousingFinanceivSequence";

    public const string SettlementSequence = "SettlementSequence";

    public const string LeaseTransfer63Sequence = "LeaseTransfer63Sequence";
    public const string LeaseTransfer63RuralAndUrbanSequence = "LeaseTransfer63RuralAndUrbanSequence";

    public const string Release55ASequence = "Release55ASequence";
    public const string Release55ARuralAndUrbanSequence = "Release55ARuralAndUrbanSequence";
    public const string RespondentiaBond56Sequence = "RespondentiaBond56Sequence";

    public const string Rectification159Sequence = "Rectification159Sequence";
    public const string SurrenderOfLeaseSequence = "SurrenderOfLeaseSequence";
    public const string DeedDepositAgreementSequence = "DeedDepositAgreementSequence";
    public const string Transfer62bSequence = "Transfer62bSequence";



    public int GetNextValueOfSequence(string sequenceName)
    {
        SqlParameter result = new SqlParameter("@result", System.Data.SqlDbType.Int)
        {
            Direction = System.Data.ParameterDirection.Output
        };
        Database.ExecuteSqlRaw($"SELECT @result = (NEXT VALUE FOR dbo.{sequenceName})", result);
        return (int)result.Value;
    }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder); // Important: Keep this!

        // Configure 1-to-1 relationship with shared primary key

        modelBuilder.Entity<AssignedTasks>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<PropertyDetail>()
         .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Court>()
           .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<UserProfile>()
            .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Purpose>()
           .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<EStampMain>()
           .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<WorkFlow>()
          .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<WorkFlowStep>()
          .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<WorkFlowVerificationStep>()
          .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<WorkflowExecutionLog>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BiometricVerification>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<ErrorLog>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Payment>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<PaymentDetail>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Agreement>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BusinessContract>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<GeneralPowerOfAttorneyInfo>()
         .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<PropertyPowerOfAttorneyInfo>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Resource>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<ArticleOfClerkship>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<AgreementMemorandum>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Mortgage>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Partnership>()
      .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease>()
      .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Conveyance23>()
     .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Conveyance23RuralAndUrban>()
    .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Gift33>()
     .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Gift33RuralAndUrban>()
    .HasKey(p => p.Id); // Primary key


        modelBuilder.Entity<CertificateOfSale18>()
  .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<CertificateOfSale18RuralAndUrban>()
    .HasKey(p => p.Id); // Primary key


        modelBuilder.Entity<PowerOfAttorney48b>()
  .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<PowerOfAttorney48bRuralAndUrban>()
    .HasKey(p => p.Id); // Primary key


        modelBuilder.Entity<AgreementMemorandum5CC>()
    .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<AgreementMemorandum5CCRuralAndUrban>()
    .HasKey(p => p.Id); // Primary key


        modelBuilder.Entity<Transfer>()
     .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BillOfExchange>()
     .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BillOfExchange13A2>()
   .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BillOfExchange13B1>()
   .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BillOfExchange13B2>()
   .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<BillOfExchange13B3>()
   .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Award>()
   .HasKey(p => p.Id); // Primary key
        modelBuilder.Entity<Partition>()
   .HasKey(p => p.Id); // Primary key
        modelBuilder.Entity<Bond>()
    .HasKey(p => p.Id); // Primary key
        modelBuilder.Entity<Cancellation>()
    .HasKey(p => p.Id); // Primary key
        modelBuilder.Entity<BottomryBond>()
    .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<AuthenticatedDeclaration>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Decree27A>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Decree27ARuralAndUrban>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease352A>()
     .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease352ARuralAndUrban>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease351B>()
    .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease351BRuralAndUrban>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease351C1D>()
    .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Lease351C1DRuralAndUrban>()
       .HasKey(p => p.Id); // Primary key


        modelBuilder.Entity<FurtherCharge>()
      .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<ShareWarrent59>()
      .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<NoteOrMemorandum>()
      .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<DebentureOrParticipationTerm>()
      .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<ExchangeOfImmovableProperty31>()
         .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<ExchangeOfImmovableProperty31RuralAndUrban>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<LeaseTransfer63>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<LeaseTransfer63RuralAndUrban>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Release55A>()
     .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Release55ARuralAndUrban>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<PromissoryNote>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Rectification159>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<SurrenderOfLease>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Settlement>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Divorce29>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<MortgageForHousingFinanceiv>()
       .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<RespondentiaBond56>()
       .HasKey(p => p.Id); // Primary key


        modelBuilder.Entity<DeedDepositAgreement>()
        .HasKey(p => p.Id); // Primary key

        modelBuilder.Entity<Transfer62b>()
       .HasKey(p => p.Id); // Primary key 

        modelBuilder.Entity<UserProfile>()
           .HasOne(p => p.ApplicationUser) // navigation to ApplicationUser
           .WithOne(u => u.Profile) // navigation from ApplicationUser
           .HasForeignKey<UserProfile>(p => p.Id) // FK is also PK
           .OnDelete(DeleteBehavior.Cascade); // optional: delete profile if user is deleted

        modelBuilder.Entity<WorkFlowStep>()
          .HasOne(m => m.WorkFlow)
          .WithMany()
          .HasForeignKey(m => m.WorkFlowId);

        modelBuilder.Entity<WorkFlowVerificationStep>()
          .HasOne(m => m.WorkFlow)
          .WithMany()
          .HasForeignKey(m => m.WorkFlowId);

        modelBuilder.Entity<WorkflowExecutionLog>()
         .HasOne(m => m.WorkflowStep)
         .WithMany()
         .HasForeignKey(m => m.WorkflowStepId);

        modelBuilder.Entity<EStampMain>()
         .HasOne(m => m.WorkFlow)
         .WithMany()
         .HasForeignKey(m => m.WorkflowId);

        modelBuilder.Entity<EStampMain>()
         .HasOne(m => m.Purpose)
         .WithMany()
         .HasForeignKey(m => m.PurposeId);

        modelBuilder.Entity<CourtFee>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Parties>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<StampInfo>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BusinessContract>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<GeneralPowerOfAttorneyInfo>()
        .HasOne(m => m.EStampMain)
        .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<PropertyPowerOfAttorneyInfo>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Payment>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<PropertyDetail>()
      .HasOne(m => m.EStampMain)
      .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<PaymentDetail>()
      .HasOne(m => m.EStampMain)
      .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Resource>()
         .HasOne(m => m.EStampMain)
         .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<ArticleOfClerkship>()
        .HasOne(m => m.EStampMain)
        .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<AgreementMemorandum>()
        .HasOne(m => m.EStampMain)
        .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Mortgage>()
        .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Partnership>()
       .HasOne(m => m.EStampMain)
      .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Lease>()
     .HasOne(m => m.EStampMain)
    .WithMany()
    .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Conveyance23>()
   .HasOne(m => m.EStampMain)
  .WithMany()
  .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Conveyance23RuralAndUrban>()
   .HasOne(m => m.EStampMain)
  .WithMany()
  .HasForeignKey(m => m.EStampMainId);



        modelBuilder.Entity<CertificateOfSale18>()
.HasOne(m => m.EStampMain)
.WithMany()
.HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<CertificateOfSale18RuralAndUrban>()
   .HasOne(m => m.EStampMain)
  .WithMany()
  .HasForeignKey(m => m.EStampMainId);





        modelBuilder.Entity<AgreementMemorandum5CC>()
  .HasOne(m => m.EStampMain)
 .WithMany()
 .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<AgreementMemorandum5CCRuralAndUrban>()
   .HasOne(m => m.EStampMain)
  .WithMany()
  .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Gift33>()
.HasOne(m => m.EStampMain)
.WithMany()
.HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Gift33RuralAndUrban>()
   .HasOne(m => m.EStampMain)
  .WithMany()
  .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<PowerOfAttorney48b>()
.HasOne(m => m.EStampMain)
.WithMany()
.HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<PowerOfAttorney48bRuralAndUrban>()
   .HasOne(m => m.EStampMain)
  .WithMany()
  .HasForeignKey(m => m.EStampMainId);


        modelBuilder.Entity<Transfer>()
        .HasOne(m => m.EStampMain)
        .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BillOfExchange>()
      .HasOne(m => m.EStampMain)
      .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BillOfExchange13A2>()
    .HasOne(m => m.EStampMain)
    .WithMany()
    .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BillOfExchange13B1>()
    .HasOne(m => m.EStampMain)
    .WithMany()
    .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BillOfExchange13B2>()
    .HasOne(m => m.EStampMain)
    .WithMany()
    .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BillOfExchange13B3>()
    .HasOne(m => m.EStampMain)
    .WithMany()
    .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Award>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Partition>()
       .HasOne(m => m.EStampMain)
       .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Bond>()
      .HasOne(m => m.EStampMain)
      .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Cancellation>()
      .HasOne(m => m.EStampMain)
      .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<BottomryBond>()
    .HasOne(m => m.EStampMain)
    .WithMany()
    .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<AuthenticatedDeclaration>()
         .HasOne(m => m.EStampMain)
         .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Decree27A>()
        .HasOne(m => m.EStampMain)
        .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<FurtherCharge>()
        .HasOne(m => m.EStampMain)
        .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Decree27ARuralAndUrban>()
      .HasOne(m => m.EStampMain)
      .WithMany()
     .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Lease352A>()
      .HasOne(m => m.EStampMain)
      .WithMany()
     .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Lease352ARuralAndUrban>()
      .HasOne(m => m.EStampMain)
      .WithMany()
     .HasForeignKey(m => m.EStampMainId);


        modelBuilder.Entity<Lease351B>()
  .HasOne(m => m.EStampMain)
  .WithMany()
 .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Lease351BRuralAndUrban>()
      .HasOne(m => m.EStampMain)
      .WithMany()
     .HasForeignKey(m => m.EStampMainId);


        modelBuilder.Entity<Lease351C1D>()
  .HasOne(m => m.EStampMain)
  .WithMany()
 .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Lease351C1DRuralAndUrban>()
      .HasOne(m => m.EStampMain)
      .WithMany()
     .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<ShareWarrent59>()
        .HasOne(m => m.EStampMain)
        .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<NoteOrMemorandum>()
       .HasOne(m => m.EStampMain)
       .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<DebentureOrParticipationTerm>()
       .HasOne(m => m.EStampMain)
       .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<ExchangeOfImmovableProperty31>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<ExchangeOfImmovableProperty31RuralAndUrban>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<PromissoryNote>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Rectification159>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<SurrenderOfLease>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<LeaseTransfer63>()
       .HasOne(m => m.EStampMain)
       .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<LeaseTransfer63RuralAndUrban>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Release55A>()
     .HasOne(m => m.EStampMain)
     .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Release55ARuralAndUrban>()
        .HasOne(m => m.EStampMain)
        .WithMany()
         .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Divorce29>()
       .HasOne(m => m.EStampMain)
       .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<MortgageForHousingFinanceiv>()
      .HasOne(m => m.EStampMain)
      .WithMany()
       .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Settlement>()
     .HasOne(m => m.EStampMain)
     .WithMany()
      .HasForeignKey(m => m.EStampMainId);


        modelBuilder.HasSequence<int>(PropertyDetailSequence, schema: "dbo")
          .StartsAt(1)
          .IncrementsBy(1);

        modelBuilder.Entity<RespondentiaBond56>()
     .HasOne(m => m.EStampMain)
     .WithMany()
      .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<DeedDepositAgreement>()
       .HasOne(m => m.EStampMain)
       .WithMany()
        .HasForeignKey(m => m.EStampMainId);

        modelBuilder.Entity<Transfer62b>()
     .HasOne(m => m.EStampMain)
     .WithMany()
      .HasForeignKey(m => m.EStampMainId);


        modelBuilder.HasSequence<int>(AssignedTaskSequence, schema: "dbo")
           .StartsAt(1)
           .IncrementsBy(1);

        modelBuilder.HasSequence<int>(CourtSequence, schema: "dbo")
            .StartsAt(1)
            .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PurposeSequence, schema: "dbo")
             .StartsAt(1)
             .IncrementsBy(1);

        modelBuilder.HasSequence<int>(WorkFlowSequence, schema: "dbo")
             .StartsAt(1)
             .IncrementsBy(1);


        modelBuilder.HasSequence<int>(WorkFlowStepSequence, schema: "dbo")
            .StartsAt(1)
            .IncrementsBy(1);


        modelBuilder.HasSequence<int>(WorkFlowVerificationStepSequence, schema: "dbo")
            .StartsAt(1)
            .IncrementsBy(1);

        modelBuilder.HasSequence<int>(EStampMainSequence, schema: "dbo")
            .StartsAt(1)
            .IncrementsBy(1);

        modelBuilder.HasSequence<int>(CourtFeeSequence, schema: "dbo")
           .StartsAt(1)
           .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BiometricSequence, schema: "dbo")
           .StartsAt(1)
           .IncrementsBy(1);

        modelBuilder.HasSequence<long>(ErrorLogSequence, schema: "dbo")
           .StartsAt(1)
           .IncrementsBy(1);

        modelBuilder.HasSequence<int>(AgreementSequence, schema: "dbo")
           .StartsAt(1)
           .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PartiesSequence, schema: "dbo")
         .StartsAt(1)
         .IncrementsBy(1);

        modelBuilder.HasSequence<int>(StampInfoSequence, schema: "dbo")
       .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BusinessContractSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(GeneralPowerOfAttorneyInfoSequence, schema: "dbo")
       .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PaymentSequence, schema: "dbo")
       .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PaymentDetailSequence, schema: "dbo")
       .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PropertyPowerOfAttorneyInfoSequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(ResourceSequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(WorkFlowExecutionLogSequence, schema: "dbo")
    .StartsAt(1)
    .IncrementsBy(1);

        modelBuilder.HasSequence<int>(ArticleOfClerkshipSequence, schema: "dbo")
    .StartsAt(1)
    .IncrementsBy(1);

        modelBuilder.HasSequence<int>(AgreementMemorandumSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(MortgageSequence, schema: "dbo")
       .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PartnershipSequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(LeaseSequence, schema: "dbo")
   .StartsAt(1)
   .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Conveyance23Sequence, schema: "dbo")
 .StartsAt(1)
 .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Conveyance23RuralAndUrbansSequence, schema: "dbo")
 .StartsAt(1)
 .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Gift33Sequence, schema: "dbo")
.StartsAt(1)
.IncrementsBy(1);

        modelBuilder.HasSequence<int>(Gift33RuralAndUrbanSequence, schema: "dbo")
 .StartsAt(1)
 .IncrementsBy(1);


        modelBuilder.HasSequence<int>(POA48bSequence, schema: "dbo")
.StartsAt(1)
.IncrementsBy(1);

        modelBuilder.HasSequence<int>(POA48bRuralAndUrbanSequence, schema: "dbo")
 .StartsAt(1)
 .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Memorandum5ccSequence, schema: "dbo")
.StartsAt(1)
.IncrementsBy(1);

        modelBuilder.HasSequence<int>(Memorandum5ccRuralAndUrbanSequence, schema: "dbo")
 .StartsAt(1)
 .IncrementsBy(1);


        modelBuilder.HasSequence<int>(TransferSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BillofExchangeSequence, schema: "dbo")
      .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BillofExchange13A2Sequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BillofExchange13B1Sequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BillofExchange13B2Sequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BillofExchange13B3Sequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);
        modelBuilder.HasSequence<int>(AwardSequence, schema: "dbo")
    .StartsAt(1)
    .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PartitionSequence, schema: "dbo")
    .StartsAt(1)
    .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BondSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(CancellationSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(BottomryBondSequence, schema: "dbo")
      .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(AuthenticatedDeclarationSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Decree27ASequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Decree27ARuralAndUrbanSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Lease352ASequence, schema: "dbo")
      .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Lease352ARuralAndUrbanSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);


        modelBuilder.HasSequence<int>(CertificateOfSale18Sequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(CertificateOfSale18RuralAndUrbanSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);


        modelBuilder.HasSequence<int>(Lease351BSequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Lease351BRuralAndUrbanSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Lease351C1DSequence, schema: "dbo")
     .StartsAt(1)
     .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Lease351C1DRuralAndUrbanSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(FurtherChargeSequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(ShareWarrent59Sequence, schema: "dbo")
       .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(NoteOrMemorandumSequence, schema: "dbo")
      .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(DebentureOrParticipationTermSequence, schema: "dbo")
      .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(ExchangeOfImmovableProperty31Sequence, schema: "dbo")
        .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(ExchangeOfImmovableProperty31RuralAndUrbanSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(PromissoryNoteSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Rectification159Sequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(SurrenderOfLeaseSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(LeaseTransfer63Sequence, schema: "dbo")
        .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(LeaseTransfer63RuralAndUrbanSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Release55ASequence, schema: "dbo")
      .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Release55ARuralAndUrbanSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

         modelBuilder.HasSequence<int>(Divorce29Sequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(MortgageForHousingFinanceivSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);


        modelBuilder.HasSequence<int>(SettlementSequence, schema: "dbo")
         .StartsAt(1)
        .IncrementsBy(1);

        modelBuilder.HasSequence<int>(RespondentiaBond56Sequence, schema: "dbo")
        .StartsAt(1)
       .IncrementsBy(1);

        modelBuilder.HasSequence<int>(DeedDepositAgreementSequence, schema: "dbo")
       .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.HasSequence<int>(Transfer62bSequence, schema: "dbo")
       .StartsAt(1)
      .IncrementsBy(1);

        modelBuilder.Entity<Transfer>()
        .Property(t => t.TotalAmount)
        .HasPrecision(18, 2);

        modelBuilder.Entity<StampInfo>()
        .Property(t => t.Amount)
        .HasPrecision(18, 2);

        modelBuilder.Entity<ShareWarrent59>()
       .Property(t => t.ConsiderationAmount)
       .HasPrecision(18, 2);

        modelBuilder.Entity<Resource>()
      .Property(t => t.FileSize)
      .HasPrecision(18, 2);

        modelBuilder.Entity<PropertyPowerOfAttorneyInfo>()
    .Property(t => t.FeeAmount)
    .HasPrecision(18, 2);

        modelBuilder.Entity<PowerOfAttorney48b>()
   .Property(t => t.StructureValue)
   .HasPrecision(18, 2);

        modelBuilder.Entity<Payment>()
   .Property(t => t.Amount)
   .HasPrecision(18, 2);

        modelBuilder.Entity<PaymentDetail>()
  .Property(t => t.Amount)
  .HasPrecision(18, 2);

        modelBuilder.Entity<Partition>()
 .Property(t => t.CompensationAmount)
 .HasPrecision(18, 2);

        modelBuilder.Entity<Mortgage>()
.Property(t => t.LoanAmount)
.HasPrecision(18, 2);

        modelBuilder.Entity<Mortgage>()
.Property(t => t.FeeAmount)
.HasPrecision(18, 2);


        modelBuilder.Entity<Lease352A>()
.Property(t => t.LandValue)
.HasPrecision(18, 2);

        modelBuilder.Entity<ExchangeOfImmovableProperty31>()
        .Property(t => t.StructureValue)
        .HasPrecision(18, 2);

        modelBuilder.Entity<LeaseTransfer63>()
       .Property(t => t.StructureValue)
       .HasPrecision(18, 2);

        modelBuilder.Entity<Release55A>()
      .Property(t => t.StructureValue)
      .HasPrecision(18, 2);
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                        entry.Entity.CreatedBy = _authenticatedUserId.UserId;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                        entry.Entity.UpdatedBy = _authenticatedUserId.UserId;
                    break;
                case EntityState.Deleted:
                    //entry.Entity.DeletedAt = DateTime.UtcNow;
                    //entry.Entity.IsDelete = true;
                    //if (!string.IsNullOrEmpty(authenticatedUserId?.UserId))
                    //    entry.Entity.DeletedBy = authenticatedUserId.UserId;
                    break;
            }
        }
        return base.SaveChangesAsync(cancellationToken);
    }


    public override int SaveChanges()
    {
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                        entry.Entity.CreatedBy = _authenticatedUserId.UserId;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                        entry.Entity.UpdatedBy = _authenticatedUserId.UserId;
                    break;
                case EntityState.Deleted:
                    //entry.Entity.DeletedAt = DateTime.UtcNow;
                    //entry.Entity.IsDelete = true;
                    //if (!string.IsNullOrEmpty(authenticatedUserId?.UserId))
                    //    entry.Entity.DeletedBy = authenticatedUserId.UserId;
                    break;
            }
        }
        return base.SaveChanges();
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {


        base.OnConfiguring(optionsBuilder);
        // Enable batching for better performance


        optionsBuilder.ConfigureWarnings(warnings =>
        {
            warnings.Log(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning);
        });
    }
}
