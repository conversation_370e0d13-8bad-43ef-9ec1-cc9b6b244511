﻿using PLRA.ESORS.Framework;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.ServicesContracts.Features.Workflow;
namespace PLRA.ESORS.Server.DataServices.Features.Workflow;
public class WorkFlowBuilderServerSideListingDataService : ServerSideListingDataService<WorkFlowBuilderListingBusinessObject, WorkFlowBuilderFilterBusinessObject>, IWorkFlowBuilderListingDataService
{

    private readonly ApplicationDbContext _context;

    public WorkFlowBuilderServerSideListingDataService(ApplicationDbContext context)
    {
        _context = context;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<WorkFlowBuilderListingBusinessObject> GetQuery(WorkFlowBuilderFilterBusinessObject filterBusinessObject)
    {
        var query = from p in _context.WorkFlows
                    where string.IsNullOrEmpty(filterBusinessObject.SearchKey)
                     || p.Name.Contains(filterBusinessObject.SearchKey)
                    select new WorkFlowBuilderListingBusinessObject
                    {
                        Id = p.Id,
                        Description = p.Description,
                        Name = p.Name,
                        IsActive = p.IsActive,
                        RequiresVerification = p.RequiresVerification
                    };

        return query;
    }
}
