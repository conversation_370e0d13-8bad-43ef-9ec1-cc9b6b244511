using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.UIServices;
using PLRA.ESORS.Server.DataServices;
using PLRA.ESORS.ServicesContracts.Features.Dashboard;
using PLRA.ESORS.ServicesContracts.Features.ParentCategory;
using PLRA.ESORS.ServicesContracts.Features.SubCategory;
using PLRA.ESORS.Server.DataServices.Features.SubCategory;
using PLRA.ESORS.ServiceContracts.SelectList;
using PLRA.ESORS.Server.DataServices.SelectList;
using PLRA.ESORS.ServicesContracts.Features.ThirdLevelCategory;
using PLRA.ESORS.Server.DataServices.Features.ThirdLevelCategory;
using PLRA.ESORS.Server.DataServices.Features.DeedsManagement.ParentCategory.Form;
using PLRA.ESORS.Server.DataServices.Features.DeedsManagement.ParentCategory.Listing;
using PLRA.ESORS.ServicesContracts.Features.Workflow;
using PLRA.ESORS.Server.DataServices.Features.Workflow;
using PLRA.ESORS.Framework.DataProtections;
using PLRA.ESORS.ServiceContracts.Features.Court;
using PLRA.ESORS.Server.DataServices.Features.Court;
using PLRA.ESORS.ServiceContracts;
using PLRA.ESORS.Server.DataServices.Services.TerritoryService;
using PLRA.ESORS.ServiceContracts.Features.CourtDelete;
                                                               using PLRA.ESORS.Server.DataServices.Features.CourtDelete;
                                                               using PLRA.ESORS.ServiceContracts.Features.WorkflowVerification;
                                                               using PLRA.ESORS.Server.DataServices.Features.WorkflowVerification;
                                                               using PLRA.ESORS.ServiceContracts;
                                                               using PLRA.ESORS.Server.DataServices.Services.ErrorLogging;
                                                               //##NewServiceNamespace##

namespace PLRA.ESORS.Server.WebApp
{
    public static class ServiceRegistrar
    {
        public static void RegisterServices(this IServiceCollection services, ConfigurationManager configuration)
        {

            services.Configure<TerritorySettings>(configuration.GetSection("TerritorySettings"));
            services.AddHttpClient<IStaticTerritoryService, StaticTerritoryService>();

            services.AddSingleton<AlertService>();
            services.AddScoped<KtDialogService>();
            services.AddScoped<KtNotificationService>();
            services.AddScoped<IMessageCenter, MessageCenter>();
            services.AddScoped<IAuthenticatedUser, ServerAuthenticatedUser>();
            services.AddScoped<ILocalStorageService, LocalStorageService>();
            services.AddScoped<IDataProtection, DataProtection>();
            services.AddScoped<ISelectListDataService, SelectListDataService>();
            services.AddScoped<IErrorLoggingService, ErrorLoggingServerSideDataService>();

            services.AddScoped<IDashboardListingDataService, DashboardServerSideListingDataService>();
            services.AddScoped<IParentCategoryFormDataService, ParentCategoryServerSideFormDataService>();
            services.AddScoped<IParentCategoryListingDataService, ParentCategoryServerSideListingDataService>();
            services.AddScoped<IParentCategoryDeleteFormDataService, ParentCategoryDeleteServerSideFormDataService>();
            services.AddScoped<ISubCategoryFormDataService, SubCategoryServerSideFormDataService>();
            services.AddScoped<ISubCategoryListingDataService, SubCategoryServerSideListingDataService>();
            services.AddScoped<ISubCategoryDeleteFormDataService, SubCategoryDeleteServerSideFormDataService>();
            services.AddScoped<IThirdLevelCategoryFormDataService, ThirdLevelCategoryServerSideFormDataService>();
            services.AddScoped<IThirdLevelCategoryListingDataService, ThirdLevelCategoryServerSideListingDataService>();
            services.AddScoped<IThirdLevelCategoryDeleteFormDataService, ThirdLevelCategoryDeleteServerSideFormDataService>();

            services.AddScoped<IParentCategoryDeleteFormDataService, ParentCategoryDeleteServerSideFormDataService>();
            services.AddScoped<ISubCategoryFormDataService, SubCategoryServerSideFormDataService>();
            services.AddScoped<ISubCategoryListingDataService, SubCategoryServerSideListingDataService>();
            services.AddScoped<ISubCategoryDeleteFormDataService, SubCategoryDeleteServerSideFormDataService>();
            services.AddScoped<IThirdLevelCategoryFormDataService, ThirdLevelCategoryServerSideFormDataService>();
            services.AddScoped<IThirdLevelCategoryListingDataService, ThirdLevelCategoryServerSideListingDataService>();
            services.AddScoped<IThirdLevelCategoryDeleteFormDataService, ThirdLevelCategoryDeleteServerSideFormDataService>();

            services.AddScoped<IWorkFlowStepsFormDataService, WorkFlowStepsServerSideFormDataService>();
            services.AddScoped<IWorkFlowStepsListingDataService, WorkFlowStepsServerSideListingDataService>();
            services.AddScoped<IWorkFlowBuilderFormDataService, WorkFlowBuilderServerSideFormDataService>();
            services.AddScoped<IWorkFlowBuilderListingDataService, WorkFlowBuilderServerSideListingDataService>();

            services.AddScoped<IWorkFlowBuilderDeleteFormDataService, WorkFlowBuilderDeleteServerSideFormDataService>();
            services.AddScoped<IWorkFlowStepDeleteFormDataService, WorkFlowStepDeleteServerSideFormDataService>();
            services.AddScoped<ICourtFormDataService, CourtServerSideFormDataService>();
            services.AddScoped<ICourtListingDataService, CourtServerSideListingDataService>();
            services.AddScoped<ICourtDeleteFormDataService, CourtDeleteServerSideFormDataService>(); 
 services.AddScoped<IWorkflowVerificationFormDataService, WorkflowVerificationServerSideFormDataService>(); 
 services.AddScoped<IWorkflowVerificationListingDataService, WorkflowVerificationServerSideListingDataService>(); 
 //##NewService##
        }
    }
}
