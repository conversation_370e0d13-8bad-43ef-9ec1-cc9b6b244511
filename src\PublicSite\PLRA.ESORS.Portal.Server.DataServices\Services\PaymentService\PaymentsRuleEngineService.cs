using PLRA.ESORS.Portal.ServiceContracts.Models;
using System.Text.Json.Serialization;
using System.Text.Json;

namespace PLRA.ESORS.Portal.Server.DataServices.Services
{
    public class PaymentsRuleEngineService
    {
        
        private PaymentRuleConfig RuleEngine { get; set; }
        public PaymentsRuleEngineService()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "wwwroot", "PaymentRules.json");
            var json = File.ReadAllText(filePath);
            var ruleEngine = FromJson(json);
            RuleEngine = ruleEngine;
        }

        public static PaymentRuleConfig FromJson(string json)
        {
            return JsonSerializer.Deserialize<PaymentRuleConfig>(json)!;
        }

        public string ToJson()
        {
            return JsonSerializer.Serialize(this);
        }

        public PaymentRecord Calculate(int workflowId, decimal amount, decimal propertyValue = 0)
        {
            var deed = RuleEngine.Deeds.FirstOrDefault(x=>x.WorkFlowId == workflowId)!;
            if (deed == null) throw new Exception("No Rule Engine found");

            decimal stampDuty = 0, registrationFee = 0, cvt = 0, fbr236KFees = 0, fbr236CFees = 0, ttipFees = 0;

            if (deed == null) throw new ArgumentNullException(nameof(deed));

            if (deed.HasStampDuty)
            {
                var duty = deed.Fees.StampDuty;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        stampDuty = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        stampDuty = ApplyMinMax(percentageAmount, duty.MinAmount, percentageAmount);
                        break;
                    case "UserDefined":
                        stampDuty = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        stampDuty = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.HasRegistrationFee)
            {
                var duty = deed.Fees.RegistrationFee;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        registrationFee = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        registrationFee = ApplyMinMax(percentageAmount, duty.MinAmount, duty.MaxAmount);
                        break;
                    case "UserDefined":
                        registrationFee = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        registrationFee = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.hasCVT)
            {

            }
            if (deed.HasFBRFees)
            {

            }
            if (deed.HasTTIPFees)
            {

            }

            return new PaymentRecord(deed, stampDuty, registrationFee, cvt, fbr236KFees, fbr236CFees, ttipFees);
        }

        private decimal ApplyMinMax(decimal amount, decimal? min, decimal? max)
        {
            if (min.HasValue && amount < min.Value) return min.Value;
            if (max.HasValue && amount > max.Value) return max.Value;
            return amount;
        }

        private decimal CalculateUserDefined(Deed deed, decimal amount, decimal propertyValue)
        {
            // Court fee specific calculation 
            if (amount > deed.Fees.StampDuty.MinAmount && amount <= deed.Fees.StampDuty.MaxAmount)
            {
                return ApplyMinMax(amount, deed.Fees.StampDuty.MinAmount, deed.Fees.StampDuty.MaxAmount);
            }
            throw new ArgumentException($"{deed.Fees.StampDuty.CalculationRule}");
        }

        private decimal CalculateByRule(Deed deed, FeeCalculation duty, decimal amount, decimal propertyValue)
        {
            // Get the calculation rule from the duty (either StampDuty or RegistrationFee)
            var calculationRule = duty.CalculationRule;

            if (string.IsNullOrEmpty(calculationRule))
            {
                return duty.MinAmount ?? 0;
            }

            // Handle rules based on specific workflow IDs to avoid conflicts
            switch (deed.WorkFlowId)
            {
                case 63: // LEASE - 35(1(a))
                    return CalculateWorkflow63Rules(duty, calculationRule, amount, propertyValue);

                case 64: // LEASE - 35(1(b))
                    return CalculateWorkflow64Rules(duty, calculationRule, amount, propertyValue);

                case 65: // LEASE - 35(1(c))
                    return CalculateWorkflow65Rules(duty, calculationRule, amount, propertyValue);

                case 66: // LEASE - 35(1(d))
                    return CalculateWorkflow66Rules(duty, calculationRule, amount, propertyValue);

                case 67: // LEASE - 35(1(e))
                    return CalculateWorkflow67Rules(duty, calculationRule, amount, propertyValue);

                case 68: // LEASE - 35(1(f))
                    return CalculateWorkflow68Rules(duty, calculationRule, amount, propertyValue);

                case 69: // LEASE - 35(1(g))
                    return CalculateWorkflow69Rules(duty, calculationRule, amount, propertyValue);

                case 70: // LEASE - 35(1(h))
                    return CalculateWorkflow70Rules(duty, calculationRule, amount, propertyValue);

                case 71: // LEASE - 35(1(i))
                    return CalculateWorkflow71Rules(duty, calculationRule, amount, propertyValue);

                case 72: // LEASE - 35(1(j))
                    return CalculateWorkflow72Rules(duty, calculationRule, amount, propertyValue);

                case 73: // LEASE - 35(1(k))
                    return CalculateWorkflow73Rules(duty, calculationRule, amount, propertyValue);

                case 74: // LEASE - 35(1(l))
                    return CalculateWorkflow74Rules(duty, calculationRule, amount, propertyValue);

                case 75: // LEASE - 35(1(m))
                    return CalculateWorkflow75Rules(duty, calculationRule, amount, propertyValue);

                case 76: // LEASE - 35(1(n))
                    return CalculateWorkflow76Rules(duty, calculationRule, amount, propertyValue);

                case 77: // LEASE - 35(1(o))
                    return CalculateWorkflow77Rules(duty, calculationRule, amount, propertyValue);

                case 78: // LEASE - 35(1(p))
                    return CalculateWorkflow78Rules(duty, calculationRule, amount, propertyValue);

                case 79: // LEASE - 35(1(q))
                    return CalculateWorkflow79Rules(duty, calculationRule, amount, propertyValue);

                case 80: // LEASE - 35(1(r))
                    return CalculateWorkflow80Rules(duty, calculationRule, amount, propertyValue);

                case 81: // LEASE - 35(1(s))
                    return CalculateWorkflow81Rules(duty, calculationRule, amount, propertyValue);

                case 82: // LEASE - 35(1(t))
                    return CalculateWorkflow82Rules(duty, calculationRule, amount, propertyValue);

                case 83: // LEASE - 35(1(u))
                    return CalculateWorkflow83Rules(duty, calculationRule, amount, propertyValue);

                case 84: // LEASE - 35(1(v))
                    return CalculateWorkflow84Rules(duty, calculationRule, amount, propertyValue);

                case 85: // LEASE - 35(1(w))
                    return CalculateWorkflow85Rules(duty, calculationRule, amount, propertyValue);

                case 86: // LEASE - 35(1(x))
                    return CalculateWorkflow86Rules(duty, calculationRule, amount, propertyValue);

                case 87: // LEASE - 35(1(y))
                    return CalculateWorkflow87Rules(duty, calculationRule, amount, propertyValue);

                case 88: // LEASE - 35(1(z))
                    return CalculateWorkflow88Rules(duty, calculationRule, amount, propertyValue);

                // Add more workflow cases as needed
                default:
                    return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
            }
        }

        #region Workflow-Specific Rule Calculations

        /// <summary>
        /// Calculate rules for Workflow 63: LEASE - 35(1(a))
        /// Registration Fee Rule: Rs.500/-, if amount ≤ Rs.500,000/-; Rs.1000/-, if amount > Rs.500,000/-
        /// </summary>
        private decimal CalculateWorkflow63Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule for LEASE - 35(1(a))
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Fallback to generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 64: LEASE - 35(1(b))
        /// </summary>
        private decimal CalculateWorkflow64Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 64 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 65: LEASE - 35(1(c))
        /// </summary>
        private decimal CalculateWorkflow65Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 65 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 66: LEASE - 35(1(d))
        /// </summary>
        private decimal CalculateWorkflow66Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 66 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 67: LEASE - 35(1(e))
        /// </summary>
        private decimal CalculateWorkflow67Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 67 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 68: LEASE - 35(1(f))
        /// </summary>
        private decimal CalculateWorkflow68Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 68 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 69: LEASE - 35(1(g))
        /// </summary>
        private decimal CalculateWorkflow69Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 69 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 70: LEASE - 35(1(h))
        /// </summary>
        private decimal CalculateWorkflow70Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 70 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 71: LEASE - 35(1(i))
        /// </summary>
        private decimal CalculateWorkflow71Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 71 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 72: LEASE - 35(1(j))
        /// </summary>
        private decimal CalculateWorkflow72Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 72 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 73: LEASE - 35(1(k))
        /// </summary>
        private decimal CalculateWorkflow73Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 73 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 74: LEASE - 35(1(l))
        /// </summary>
        private decimal CalculateWorkflow74Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 74 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 75: LEASE - 35(1(m))
        /// </summary>
        private decimal CalculateWorkflow75Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 75 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 76: LEASE - 35(1(n))
        /// </summary>
        private decimal CalculateWorkflow76Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 76 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 77: LEASE - 35(1(o))
        /// </summary>
        private decimal CalculateWorkflow77Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 77 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 78: LEASE - 35(1(p))
        /// </summary>
        private decimal CalculateWorkflow78Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 78 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 79: LEASE - 35(1(q))
        /// </summary>
        private decimal CalculateWorkflow79Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 79 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        /// <summary>
        /// Calculate rules for Workflow 80: LEASE - 35(1(r))
        /// </summary>
        private decimal CalculateWorkflow80Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Add specific rules for workflow 80 here
            // For now, use generic rules
            return CalculateGenericRules(duty, calculationRule, amount, propertyValue);
        }

        #endregion

    }

}
