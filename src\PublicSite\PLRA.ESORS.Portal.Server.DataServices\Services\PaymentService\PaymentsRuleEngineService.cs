using PLRA.ESORS.Portal.ServiceContracts.Models;
using System.Text.Json.Serialization;
using System.Text.Json;

namespace PLRA.ESORS.Portal.Server.DataServices.Services
{
    public class PaymentsRuleEngineService
    {
        
        private PaymentRuleConfig RuleEngine { get; set; }
        public PaymentsRuleEngineService()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "wwwroot", "PaymentRules.json");
            var json = File.ReadAllText(filePath);
            var ruleEngine = FromJson(json);
            RuleEngine = ruleEngine;
        }

        public static PaymentRuleConfig FromJson(string json)
        {
            return JsonSerializer.Deserialize<PaymentRuleConfig>(json)!;
        }

        public string ToJson()
        {
            return JsonSerializer.Serialize(this);
        }

        public PaymentRecord Calculate(int workflowId, decimal amount, decimal propertyValue = 0)
        {
            var deed = RuleEngine.Deeds.FirstOrDefault(x=>x.WorkFlowId == workflowId)!;
            if (deed == null) throw new Exception("No Rule Engine found");

            decimal stampDuty = 0, registrationFee = 0, cvt = 0, fbr236KFees = 0, fbr236CFees = 0, ttipFees = 0;

            if (deed == null) throw new ArgumentNullException(nameof(deed));

            if (deed.HasStampDuty)
            {
                var duty = deed.Fees.StampDuty;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        stampDuty = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        stampDuty = ApplyMinMax(percentageAmount, duty.MinAmount, percentageAmount);
                        break;
                    case "UserDefined":
                        stampDuty = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        stampDuty = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.HasRegistrationFee)
            {
                var duty = deed.Fees.RegistrationFee;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        registrationFee = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        registrationFee = ApplyMinMax(percentageAmount, duty.MinAmount, duty.MaxAmount);
                        break;
                    case "UserDefined":
                        registrationFee = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        registrationFee = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.hasCVT)
            {

            }
            if (deed.HasFBRFees)
            {

            }
            if (deed.HasTTIPFees)
            {

            }

            return new PaymentRecord(deed, stampDuty, registrationFee, cvt, fbr236KFees, fbr236CFees, ttipFees);
        }

        private decimal ApplyMinMax(decimal amount, decimal? min, decimal? max)
        {
            if (min.HasValue && amount < min.Value) return min.Value;
            if (max.HasValue && amount > max.Value) return max.Value;
            return amount;
        }

        private decimal CalculateUserDefined(Deed deed, decimal amount, decimal propertyValue)
        {
            // Court fee specific calculation 
            if (amount > deed.Fees.StampDuty.MinAmount && amount <= deed.Fees.StampDuty.MaxAmount)
            {
                return ApplyMinMax(amount, deed.Fees.StampDuty.MinAmount, deed.Fees.StampDuty.MaxAmount);
            }
            throw new ArgumentException($"{deed.Fees.StampDuty.CalculationRule}");
        }

        private decimal CalculateByRule(Deed deed, FeeCalculation duty, decimal amount, decimal propertyValue)
        {
            // Get the calculation rule from the duty (either StampDuty or RegistrationFee)
            var calculationRule = duty.CalculationRule;

            if (string.IsNullOrEmpty(calculationRule))
            {
                return duty.MinAmount ?? 0;
            }

            // Handle the most common registration fee rule pattern for workflow 63 and others
            // "Rs.500/-, if the amount of consideration does not exceed Rs.500,000/-; and Rs.1000/-, if amount of consideration exceeds Rs.500,000/-."
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Handle mortgage deed rules
            if (calculationRule.Contains("additional stamp duty shall be charged equal to the amount of the registration fee paid on the principal mortgage deed"))
            {
                // This would require additional logic to get the principal mortgage deed fee
                // For now, return the registration fee amount (typically 500 or 1000)
                return amount <= 500000 ? 500 : 1000;
            }

            // Handle urban/rural property rules
            if (calculationRule.Contains("urban area") && calculationRule.Contains("Rural area"))
            {
                // This would require property location information
                // For now, assume urban area (1%) vs rural area (3%)
                // This logic would need to be enhanced with actual property location data
                return amount * 0.01m; // Default to urban area rate
            }

            // Handle partnership capital rules
            if (calculationRule.Contains("capital of the partnership"))
            {
                return amount <= 10000 ? 200 : 1000;
            }

            // Handle specific rate-based rules
            if (calculationRule.Contains("10 rupees for every 1000"))
            {
                return Math.Max(10, (amount / 1000) * 10);
            }

            // Handle percentage-based rules in text format
            if (calculationRule.Contains("3.25%") || calculationRule.Contains("three and quarter percent"))
            {
                return amount * 3.25m / 100;
            }

            if (calculationRule.Contains("2%") || calculationRule.Contains("two percent"))
            {
                return amount * 0.02m;
            }

            if (calculationRule.Contains("3%") || calculationRule.Contains("three percent"))
            {
                return amount * 0.03m;
            }

            if (calculationRule.Contains("1%") || calculationRule.Contains("one percent"))
            {
                return amount * 0.01m;
            }

            // Handle fixed amount rules
            if (calculationRule.Contains("Rs.200/-"))
            {
                return 200;
            }

            if (calculationRule.Contains("Rs.1000/-"))
            {
                return 1000;
            }

            if (calculationRule.Contains("Rs.500/-"))
            {
                return 500;
            }

            // Default to min amount if no specific rule matches
            return duty.MinAmount ?? 0;
        }

    }

}
