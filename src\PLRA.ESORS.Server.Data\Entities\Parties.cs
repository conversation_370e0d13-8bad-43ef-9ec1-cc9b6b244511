﻿using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Server.Data.Data;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PLRA.ESORS.Server.Data.Entities
{
    public class Parties : BaseEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Id { get; set; }
        public long EStampMainId { get; set; }

        [StringLength(150)]
        public  string? Name { get; set; }

        [StringLength(50)]
        public  string? Relation {  get; set; }

        [StringLength(150)]
        public  string? RelativeName { get; set; }

        [StringLength(13)]
        public long? CNIC {  get; set; }

        [StringLength(12)]
        public long? PhoneNumber { get; set; }

        [StringLength(150)]
        public  string? Email { get; set; }

        [StringLength(250)]
        public  string? Address { get; set; }

        [StringLength(150)]
        public string? LawyerName { get; set; }
        public PARTY_TYPE Party_Type { get; set; }
        public virtual EStampMain EStampMain { get; set; }

        [StringLength(30)]
        public string? RegistrationNo { get; set; }

        [StringLength(12)]
        public long? MobileNo { get; set; }

        [StringLength(150)]
        public string? RepresentativeName { get; set; }

        [StringLength(13)]
        public long? RepresentativeCNIC { get; set; }

        [StringLength(100)]
        public string? RepresentativeDesignation { get; set; }

        [StringLength(9)]
        public string? NTN { get; set; }
        public int CompanyType { get; set; }

        /// <summary>
        /// JSON metadata storing which fields were populated from API vs manually entered
        /// </summary>
        [StringLength(1000)]
        public string? ApiFieldMetadata { get; set; }
    }
}
