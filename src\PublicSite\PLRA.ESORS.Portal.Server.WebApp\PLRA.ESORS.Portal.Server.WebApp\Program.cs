﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Portal.Razor.Layout;
using PLRA.ESORS.Portal.Server.WebApp;
using PLRA.ESORS.Portal.Server.WebApp.Components;
using PLRA.ESORS.Portal.Server.WebApp.Components.Account;
using PLRA.ESORS.Portal.Server.WebApp.Middleware;
using PLRA.ESORS.Portal.ServiceContracts.Models;
using PLRA.ESORS.Portal.ServiceContracts.SelectList;
using PLRA.ESORS.Portal.Server.DataServices.Services.NadraService;
using PLRA.ESORS.Server.Data.Data;
using System.Net;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using PLRA.ESORS.Portal.ServiceContracts;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents()
    .AddAuthenticationStateSerialization();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();



builder.Services.Configure<JsonSerializerOptions>(options =>
{
    options.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
    options.WriteIndented = true; // Optional, for pretty JSON
});

builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = IdentityConstants.ApplicationScheme;
        options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
    })
    .AddIdentityCookies();

builder.Services.AddDbContext<ApplicationDbContext>(options => options
    .UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));
    //.UseAsyncSeeding((async (context, _, _) => await DataSeeding.Seed(context)))
    //.UseSeeding(((context, _) => DataSeeding.Seed(context).GetAwaiter().GetResult())));

builder.Services.AddDatabaseDeveloperPageExceptionFilter();

builder.Services.AddIdentityCore<ApplicationUser>(options => { options.SignIn.RequireConfirmedAccount = true; }
            )
            .AddRoles<IdentityRole>()
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddSignInManager()
            .AddDefaultTokenProviders();

// Add Authorization with Role-Based Policies
builder.Services.AddAuthorizationBuilder()
    .AddPolicy("GovernmentOfficials", policy =>
        policy.RequireRole(AppRoles.Registrar, AppRoles.SubRegistrar, AppRoles.LocalCommission))
    .AddPolicy("PublicUsers", policy =>
        policy.RequireRole(AppRoles.Citizen, AppRoles.Vendor));

builder.Services.AddDistributedSqlServerCache(options =>
{
    options.ConnectionString =
    builder.Configuration.GetConnectionString("DefaultConnection");
    options.SchemaName = "dbo";
    options.TableName = "__MemCache";
});

builder.Services.AddDataProtection().SetApplicationName("eSoRS");
builder.Services.RegisterServices(builder.Configuration);


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

// Add global exception handling middleware
app.UseGlobalExceptionHandling();

app.UseAntiforgery();
app.UseStaticFiles();
app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies([typeof(PLRA.ESORS.Portal.Server.WebApp.Client._Imports).Assembly, typeof(MainLayout).Assembly]);

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();

app.MapPost("/api/verify-fingerprint", async (FingerprintRequest request,
    INadraBiometricApiService nadraBiometricService,
    PLRA.ESORS.Framework.DataProtections.IDataProtection dataProtection,
    PLRA.ESORS.Portal.ServiceContracts.IErrorLoggingService errorLoggingService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Received fingerprint verification request with template length: {TemplateLength}",
            request.FingerTemplate?.Length ?? 0);

        // Decode encrypted parameters
        var cnicDecode = dataProtection.Decode(request.CNIC);
        var cnic = Convert.ToInt64(cnicDecode.Split("---")[0]);

        var partyDecode = dataProtection.Decode(request.Party);
        var party = Convert.ToInt64(partyDecode.Split("---")[0]);

        var taskDecode = dataProtection.Decode(request.Task);
        var task = Convert.ToInt64(taskDecode.Split("---")[0]);

        logger.LogDebug("Decoded parameters - CNIC: {CNIC}, Party: {PartyId}, Task: {TaskId}", cnic, party, task);

        // Validate fingerprint template
        if (string.IsNullOrEmpty(request.FingerTemplate))
        {
            await errorLoggingService.LogValidationErrorAsync(
                "BiometricVerificationEndpoint",
                "VerifyFingerprint",
                "Empty fingerprint template provided",
                new { CNIC = cnic, PartyId = party, TaskId = task }
            );

            return Results.BadRequest(new {
                Success = false,
                Message = "Fingerprint template is required",
                StatusCode = "INVALID_TEMPLATE"
            });
        }

        // Log template characteristics for debugging
        logger.LogDebug("Fingerprint template characteristics - Length: {Length}, First 50 chars: {Preview}",
            request.FingerTemplate.Length,
            request.FingerTemplate.Length > 50 ? request.FingerTemplate.Substring(0, 50) + "..." : request.FingerTemplate);

        // Call LRMIS-style NADRA biometric verification with improved error handling
        var lrmisResult = await nadraBiometricService.VerifyFingerPrintLrmisStyleAsync(cnic, request.FingerTemplate, party, task);

        logger.LogInformation("LRMIS-style NADRA verification completed for CNIC: {CNIC}. Code: {StatusCode}, Verified: {IsVerified}",
            cnic, lrmisResult.StatusCode, lrmisResult.IsVerified);

        // Check for specific template format errors
        if (lrmisResult.StatusCode == "120" && lrmisResult.Message?.Contains("invalid input finger template") == true)
        {
            await errorLoggingService.LogValidationErrorAsync(
                "BiometricVerificationEndpoint",
                "VerifyFingerprint",
                $"NADRA rejected fingerprint template format. Code: {lrmisResult.StatusCode}, Message: {lrmisResult.Message}",
                new {
                    CNIC = cnic,
                    PartyId = party,
                    TaskId = task,
                    TemplateLength = request.FingerTemplate?.Length,
                    TemplatePreview = request.FingerTemplate?.Length > 100 ? request.FingerTemplate.Substring(0, 100) + "..." : request.FingerTemplate
                }
            );
        }

        // Check for specific template format errors
        if (lrmisResult.StatusCode == "120" && lrmisResult.Message?.Contains("invalid input finger template") == true)
        {
            await errorLoggingService.LogValidationErrorAsync(
                "BiometricVerificationEndpoint",
                "VerifyFingerprint",
                $"NADRA rejected fingerprint template format. Code: {lrmisResult.StatusCode}, Message: {lrmisResult.Message}",
                new {
                    CNIC = cnic,
                    PartyId = party,
                    TaskId = task,
                    TemplateLength = request.FingerTemplate?.Length,
                    TemplatePreview = request.FingerTemplate?.Length > 100 ? request.FingerTemplate.Substring(0, 100) + "..." : request.FingerTemplate
                }
            );
        }

        // Create LRMIS-style response for hidden field
        var nadraBiometricData = System.Text.Json.JsonSerializer.Serialize(new
        {
            StatusCode = lrmisResult.StatusCode,
            Message = lrmisResult.Message,
            IsVerified = lrmisResult.IsVerified,
            CitizenName = lrmisResult.CitizenName,
            FatherName = lrmisResult.FatherName,
            DateOfBirth = lrmisResult.DateOfBirth,
            Gender = lrmisResult.Gender,
            Address = lrmisResult.Address,
            SessionId = lrmisResult.SessionId,
            TransactionId = lrmisResult.TransactionId,
            FingerCount = lrmisResult.FingerCount,
            VerificationDateTime = lrmisResult.VerificationDateTime,
            BiometricVerificationId = lrmisResult.BiometricVerificationId,
            LrmisStyle = true
        });

        // Return LRMIS-style comprehensive response
        return Results.Ok(new
        {
            Success = lrmisResult.IsVerified,
            StatusCode = lrmisResult.StatusCode,
            Message = lrmisResult.Message,
            IsVerified = lrmisResult.IsVerified,
            CitizenName = lrmisResult.CitizenName,
            FatherName = lrmisResult.FatherName,
            DateOfBirth = lrmisResult.DateOfBirth,
            Gender = lrmisResult.Gender,
            Address = lrmisResult.Address,
            SessionId = lrmisResult.SessionId,
            TransactionId = lrmisResult.TransactionId,
            FingerCount = lrmisResult.FingerCount,
            VerificationDateTime = lrmisResult.VerificationDateTime,
            BiometricVerificationId = lrmisResult.BiometricVerificationId,
            NadraBiometricData = nadraBiometricData
        });
    }
    catch (ArgumentException argEx)
    {
        await errorLoggingService.LogValidationErrorAsync(
            "BiometricVerificationEndpoint",
            "VerifyFingerprint",
            $"Invalid argument during fingerprint verification: {argEx.Message}",
            new { Request = request }
        );

        logger.LogWarning(argEx, "Invalid argument in fingerprint verification request");
        return Results.BadRequest(new
        {
            Success = false,
            StatusCode = "INVALID_REQUEST",
            Message = argEx.Message,
            IsVerified = false
        });
    }
    catch (InvalidOperationException invEx)
    {
        await errorLoggingService.LogExternalServiceErrorAsync(
            "BiometricVerificationEndpoint",
            "VerifyFingerprint",
            invEx,
            "NADRA Biometric Service",
            "NADRA WSDL Service"
        );

        logger.LogWarning(invEx, "Invalid operation during fingerprint verification");
        return Results.BadRequest(new
        {
            Success = false,
            StatusCode = "INVALID_OPERATION",
            Message = invEx.Message,
            IsVerified = false
        });
    }
    catch (Exception ex)
    {
        await errorLoggingService.LogCriticalAsync(
            "BiometricVerificationEndpoint",
            "VerifyFingerprint",
            ex,
            new { Request = request }
        );

        logger.LogError(ex, "Unexpected error during fingerprint verification");
        return Results.Json(new
        {
            Success = false,
            StatusCode = "INTERNAL_ERROR",
            Message = "An unexpected error occurred during verification",
            IsVerified = false,
            NadraBiometricData = System.Text.Json.JsonSerializer.Serialize(new
            {
                StatusCode = "INTERNAL_ERROR",
                Message = "An unexpected error occurred during verification",
                IsVerified = false,
                VerificationDateTime = DateTime.UtcNow
            })
        }, statusCode: 500);
    }


})
.RequireAuthorization();

app.MapGet("/api/verify-parties/{data}", async (string data, PLRA.ESORS.Framework.DataProtections.IDataProtection dataProtection, IServiceScopeFactory scopeFactory, ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("API verify-parties called with data: {Data}", data);

        var dateDecode = dataProtection.Decode(data);
        logger.LogInformation("Decoded data: {DecodedData}", dateDecode);

        var arr = dateDecode.Split("--");
        var rowId = Convert.ToInt64(arr[0]);
        var verificationType = arr[1];
        var taskId = Convert.ToInt64(arr[2]);

        logger.LogInformation("Parsed values - RowId: {RowId}, VerificationType: {VerificationType}, TaskId: {TaskId}", rowId, verificationType, taskId);

        using var scope = scopeFactory.CreateScope();
        var _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        if (_context != null)
        {
            if (verificationType == "picture")
            {
                var picture = await (from p in _context.Parties
                                     from b in _context.BiometricVerifications.Where(x => x.PartyId == p.Id && x.VERIFICATION_TYPE == VERIFICATION_TYPE.CameraCapture).DefaultIfEmpty()
                                     where p.Id == rowId && p.EStampMainId == taskId
                                     select new
                                     {
                                         Id = p.Id,
                                         HasPictureAttached = b != null,
                                     }).FirstOrDefaultAsync();

                if (picture != null && picture.HasPictureAttached)
                {
                    return Results.Json(true);
                }

                return Results.Json(false);
            }

            if (verificationType == "biometric")
            {
                var picture = await (from p in _context.Parties
                                     from b in _context.BiometricVerifications.Where(x => x.PartyId == p.Id && x.VERIFICATION_TYPE == VERIFICATION_TYPE.Biometric).DefaultIfEmpty()
                                     where p.Id == rowId && p.EStampMainId == taskId
                                     select new
                                     {
                                         Id = p.Id,
                                         HasBiometricDone = b != null,
                                     }).FirstOrDefaultAsync();

                if (picture != null && picture.HasBiometricDone)
                {
                    return Results.Json(true);
                }

                return Results.Json(false);
            }

            return Results.BadRequest(new
            {
                StatusCode = HttpStatusCode.BadRequest,
                Message = "Invalid request",
            });
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error in verify-parties API with data: {Data}", data);
        return Results.Json(false);
    }
    return Results.Json(false);

})
.RequireAuthorization();

app.MapGet("/api/gettehsilbydistrictid/{districtid}", async (int districtid,
    ISelectListDataService selectListDataService) =>
{
    var list = await selectListDataService.GetTehsilByDistrictId(districtid);

    return Results.Json(list);
})
.RequireAuthorization();

app.MapGet("/api/getmauzabytehsilid/{tehsilid}", async (int tehsilid,
    ISelectListDataService selectListDataService) =>
{
    var list = await selectListDataService.GetMauzaByTehsilId(tehsilid);

    return Results.Json(list);
})
.RequireAuthorization();

app.MapGet("/api/verify-payment/{psid}", async (string psid, IServiceScopeFactory scopeFactory) =>
{
    using var scope = scopeFactory.CreateScope();
    var _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    if (_context != null)
    {
        var payment = await _context.PaymentDetails.FirstOrDefaultAsync(x => x.PSID == psid);
        if (payment != null)
        {
            return Results.Json(payment.PAYMENT_STATUS);
        }
    }
    return null;
})
.RequireAuthorization();


using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    await DataSeeding.Seed(dbContext); 
}

app.Run();
