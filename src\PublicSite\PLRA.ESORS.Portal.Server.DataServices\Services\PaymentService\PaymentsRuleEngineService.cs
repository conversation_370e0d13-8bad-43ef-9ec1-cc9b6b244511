﻿using PLRA.ESORS.Portal.ServiceContracts.Models;
using System.Text.Json.Serialization;
using System.Text.Json;

namespace PLRA.ESORS.Portal.Server.DataServices.Services
{
    public class PaymentsRuleEngineService
    {
        
        private PaymentRuleConfig RuleEngine { get; set; }
        public PaymentsRuleEngineService()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "wwwroot", "PaymentRules.json");
            var json = File.ReadAllText(filePath);
            var ruleEngine = FromJson(json);
            RuleEngine = ruleEngine;
        }

        public static PaymentRuleConfig FromJson(string json)
        {
            return JsonSerializer.Deserialize<PaymentRuleConfig>(json)!;
        }

        public string ToJson()
        {
            return JsonSerializer.Serialize(this);
        }

        public PaymentRecord Calculate(int workflowId, decimal amount, decimal propertyValue = 0)
        {
            var deed = RuleEngine.Deeds.FirstOrDefault(x=>x.WorkFlowId == workflowId)!;
            if (deed == null) throw new Exception("No Rule Engine found");

            decimal stampDuty = 0, registrationFee = 0, cvt = 0, fbr236KFees = 0, fbr236CFees = 0, ttipFees = 0;

            if (deed == null) throw new ArgumentNullException(nameof(deed));

            if (deed.HasStampDuty)
            {
                var duty = deed.Fees.StampDuty;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        stampDuty = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        stampDuty = ApplyMinMax(percentageAmount, duty.MinAmount, percentageAmount);
                        break;
                    case "UserDefined":
                        stampDuty = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        stampDuty = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.HasRegistrationFee)
            {
                var duty = deed.Fees.RegistrationFee;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        registrationFee = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        registrationFee = ApplyMinMax(percentageAmount, duty.MinAmount, duty.MaxAmount);
                        break;
                    case "UserDefined":
                        registrationFee = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        registrationFee = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.hasCVT)
            {

            }
            if (deed.HasFBRFees)
            {

            }
            if (deed.HasTTIPFees)
            {

            }

            return new PaymentRecord(deed, stampDuty, registrationFee, cvt, fbr236KFees, fbr236CFees, ttipFees);
        }

        private decimal ApplyMinMax(decimal amount, decimal? min, decimal? max)
        {
            if (min.HasValue && amount < min.Value) return min.Value;
            if (max.HasValue && amount > max.Value) return max.Value;
            return amount;
        }

        private decimal CalculateUserDefined(Deed deed, decimal amount, decimal propertyValue)
        {
            // Court fee specific calculation 
            if (amount > deed.Fees.StampDuty.MinAmount && amount <= deed.Fees.StampDuty.MaxAmount)
            {
                return ApplyMinMax(amount, deed.Fees.StampDuty.MinAmount, deed.Fees.StampDuty.MaxAmount);
            }
            throw new ArgumentException($"{deed.Fees.StampDuty.CalculationRule}");
        }

        private decimal CalculateByRule(Deed deed, FeeCalculation duty, decimal amount, decimal propertyValue)
        {
            // Get the calculation rule from the duty (either StampDuty or RegistrationFee)
            var calculationRule = duty.CalculationRule;

            if (string.IsNullOrEmpty(calculationRule))
            {
                return duty.MinAmount ?? 0;
            }

            // Handle rules based on specific workflow IDs to avoid conflicts
            switch (deed.WorkFlowId)
            {
                case 63: // LEASE - 35(1(a))
                    return CalculateWorkflow63Rules(duty, calculationRule, amount, propertyValue);

                case 138: // LEASE - 35(1(b))
                    return CalculateWorkflow138Rules(duty, calculationRule, amount, propertyValue);

                case 139: // LEASE - 35(1(c))
                    return CalculateWorkflow139Rules(duty, calculationRule, amount, propertyValue);

                case 140: // LEASE - 35(1(d))
                    return CalculateWorkflow140Rules(duty, calculationRule, amount, propertyValue);

                case 141: // LEASE - 35(2(a))
                    return CalculateWorkflow141Rules(duty, calculationRule, amount, propertyValue);

                case 142: // LEASE - 35(2(b))
                    return CalculateWorkflow142Rules(duty, calculationRule, amount, propertyValue);

                // Add more workflow cases as needed
                default:
                    throw new NotImplementedException($"Rule calculation not implemented for Workflow ID: {deed.WorkFlowId}, Deed Name: {deed.DeedName}, Calculation Rule: {calculationRule}");
            }
        }

        #region Lease Workflow-Specific Rule Calculations

        /// <summary>
        /// Calculate rules for Workflow 63: LEASE - 35(1(a))
        /// Registration Fee Rule: Rs.500/- if amount ≤ Rs.500,000/-; Rs.1000/- if amount > Rs.500,000/-
        /// Stamp Duty: 3.25% of average annual rent (Percent calculation type)
        /// </summary>
        private decimal CalculateWorkflow63Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule for LEASE - 35(1(a))
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Throw exception if rule is not implemented
            throw new NotImplementedException($"Rule calculation not implemented for Workflow 63 - LEASE 35(1(a)). Calculation Rule: {calculationRule}");
        }

        /// <summary>
        /// Calculate rules for Workflow 138: LEASE - 35(1(b))
        /// Registration Fee Rule: Rs.500/- if amount ≤ Rs.500,000/-; Rs.1000/- if amount > Rs.500,000/-
        /// Stamp Duty Rule: Urban area 1%, Rural area 3% of property value or rental values (whichever higher)
        /// </summary>
        private decimal CalculateWorkflow138Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule (same as workflow 63)
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Stamp Duty Rule for urban/rural areas
            if (calculationRule.Contains("urban area") && calculationRule.Contains("Rural area"))
            {
                // TODO: This requires property location information to determine urban vs rural
                // For now, default to urban area rate (1%)
                // This should be enhanced with actual property location data
                var higherValue = Math.Max(amount, propertyValue);
                return higherValue * 0.01m; // Default to urban area rate
            }

            // Throw exception if rule is not implemented
            throw new NotImplementedException($"Rule calculation not implemented for Workflow 138 - LEASE 35(1(b)). Calculation Rule: {calculationRule}");
        }

        /// <summary>
        /// Calculate rules for Workflow 139: LEASE - 35(1(c))
        /// Registration Fee Rule: Rs.500/- if amount ≤ Rs.500,000/-; Rs.1000/- if amount > Rs.500,000/-
        /// Stamp Duty Rule: Urban area 1%, Rural area 3% of advance amount
        /// </summary>
        private decimal CalculateWorkflow139Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule (same as workflow 63)
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Stamp Duty Rule for advance amount
            if (calculationRule.Contains("urban area") && calculationRule.Contains("Rural area") &&
                calculationRule.Contains("advance"))
            {
                // TODO: This requires property location information to determine urban vs rural
                // For now, default to urban area rate (1%)
                return amount * 0.01m; // Default to urban area rate
            }

            // Throw exception if rule is not implemented
            throw new NotImplementedException($"Rule calculation not implemented for Workflow 139 - LEASE 35(1(c)). Calculation Rule: {calculationRule}");
        }

        /// <summary>
        /// Calculate rules for Workflow 140: LEASE - 35(1(d))
        /// Registration Fee Rule: Rs.500/- if amount ≤ Rs.500,000/-; Rs.1000/- if amount > Rs.500,000/-
        /// Stamp Duty Rule: Urban area 1%, Rural area 3% of fine or premium amount
        /// </summary>
        private decimal CalculateWorkflow140Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule (same as workflow 63)
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Stamp Duty Rule for fine or premium amount
            if (calculationRule.Contains("urban area") && calculationRule.Contains("Rural area") &&
                (calculationRule.Contains("fine") || calculationRule.Contains("premium")))
            {
                // TODO: This requires property location information to determine urban vs rural
                // For now, default to urban area rate (1%)
                return amount * 0.01m; // Default to urban area rate
            }

            // Throw exception if rule is not implemented
            throw new NotImplementedException($"Rule calculation not implemented for Workflow 140 - LEASE 35(1(d)). Calculation Rule: {calculationRule}");
        }

        /// <summary>
        /// Calculate rules for Workflow 141: LEASE - 35(2(a))
        /// Registration Fee Rule: Rs.500/- if amount ≤ Rs.500,000/-; Rs.1000/- if amount > Rs.500,000/-
        /// Stamp Duty Rule: Urban area 1%, Rural area 3% of advance amount
        /// </summary>
        private decimal CalculateWorkflow141Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule (same as workflow 63)
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Stamp Duty Rule for advance amount (same as workflow 139)
            if (calculationRule.Contains("urban area") && calculationRule.Contains("Rural area") &&
                calculationRule.Contains("advance"))
            {
                // TODO: This requires property location information to determine urban vs rural
                // For now, default to urban area rate (1%)
                return amount * 0.01m; // Default to urban area rate
            }

            // Throw exception if rule is not implemented
            throw new NotImplementedException($"Rule calculation not implemented for Workflow 141 - LEASE 35(2(a)). Calculation Rule: {calculationRule}");
        }

        /// <summary>
        /// Calculate rules for Workflow 142: LEASE - 35(2(b))
        /// Registration Fee Rule: Rs.500/- if amount ≤ Rs.500,000/-; Rs.1000/- if amount > Rs.500,000/-
        /// Stamp Duty Rule: Urban area 1%, Rural area 3% of advance amount
        /// </summary>
        private decimal CalculateWorkflow142Rules(FeeCalculation duty, string calculationRule, decimal amount, decimal propertyValue)
        {
            // Registration Fee Rule (same as workflow 63)
            if (calculationRule.Contains("Rs.500/-") &&
                calculationRule.Contains("Rs.1000/-") &&
                calculationRule.Contains("Rs.500,000/-"))
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Stamp Duty Rule for advance amount (same as workflow 139 and 141)
            if (calculationRule.Contains("urban area") && calculationRule.Contains("Rural area") &&
                calculationRule.Contains("advance"))
            {
                // TODO: This requires property location information to determine urban vs rural
                // For now, default to urban area rate (1%)
                return amount * 0.01m; // Default to urban area rate
            }

            // Throw exception if rule is not implemented
            throw new NotImplementedException($"Rule calculation not implemented for Workflow 142 - LEASE 35(2(b)). Calculation Rule: {calculationRule}");
        }

        #endregion

    }

}
