﻿using PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification;
using PLRA.ESORS.Portal.ServiceContracts.Models;

namespace PLRA.ESORS.Portal.Razor
{
    public static class ComponentsDictionary
    {
        public static Dictionary<string, string> Mapper = new Dictionary<string, string>
        {
            //Court Fee Components
            {"BasicCourtInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BasicCourtInfo" },
            {"PartiesInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PaymentProcessorContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PaymentProcessors"},
            {"PartiesVerificationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"CourtFeeAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"CourtFeeAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Adhesive Stamps Components
            { "AdhesiveStampContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.StampInformation"},
            { "StampInfoDeleteFormComponent","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.StampInformation.Form"},

            //Business Contract Components
            ////Company
            {"ContractCompanyDetailInfo22AaiContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractCompanyDetailInfo22AaiiContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractCompanyDetailInfo22AaiiiContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractCompanyDetailInfo22AaivContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractCompanyDetailInfo22AavContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractCompanyDetailInfo22AbContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"BusinessContractCompanyPartiesInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"CompanyFirstPartyDeleteFormComponent","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo.Company.Form" },
            {"CompanyPartiesVerificationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BusinessContractCompanyAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BusinessContractCompanyAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            ////Individual
            {"ContractIndividualDetailInfo22AaiContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractIndividualDetailInfo22AaiiContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractIndividualDetailInfo22AaiiiiContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractIndividualDetailInfo22AaivContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractIndividualDetailInfo22AavContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"ContractIndividualDetailInfo22AbContainer"," PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo" },
            {"BusinessContractIndividualPartiesInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"IndividualPartiesVerificationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BusinessContractIndividualAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BusinessContractIndividualAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // Articles Of Clerkship 
            {"ArticlesOfClerkshipInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ArticlesOfClerkship" },
            {"PartiesInfoArticleOfClerkshipContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationArticleOfClerkshipContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"ArticleOfClerkshipAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"ArticleOfClerkshipAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },


            // POA Agreement
            {"BasicInfoPoaContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BasicPOAInfo" },
            {"PartiesInfoPoaContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationPoaContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"PoaAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"PoaAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            
             // Property POA Agreement 48(a)
            {"PropertyBasicInfoPOAContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PropertyBasicPoaInfo" },
            {"PartiesInfoPropertyPoaContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerficationPropertyPoaContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"PropertyPoaAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"PropertyPoaAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            //Agreement Memorandum 5CCC
            {"BasicInfoMemorandumCcContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementMemorandumBasicInfo" },
            {"MemorandumCompanyPartiesInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMemorandumContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MemorandumAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MemorandumAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            
            //Agreement Memorandum 5b
            {"BasicInfoMemorandum5BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementMemorandumBasicInfo" },
            {"MemorandumPartiesInfo5BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMemorandum5BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MemorandumAAgreement5BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MemorandumAgreementPreview5BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Agreement Memorandum 5c
            {"BasicInfoMemorandum5CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementMemorandumBasicInfo" },
            {"MemorandumPartiesInfo5CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMemorandum5CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MemorandumAgreement5CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MemorandumAgreementPreview5CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Agreement Memorandum 5cc
            {"BasicInfoMemorandum5CCContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementMemorandumBasicInfo" },
            {"MemorandumPartiesInfo5CCContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMemorandum5CCContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MemorandumAgreement5CCContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MemorandumAgreement5CCPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Gift 33
            {"Gift33BasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Gift33" },
            {"PartiesInfoGift33Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationGift33Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Gift33AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Gift33AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Decree 27A
            {"Decree27ABasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Decree27A" },
            {"PartiesInfoDecree27AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationDecree27AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Decree27AAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Decree27AAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              //Lease 352A, 2B
            {"Lease352ABasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"Lease352BBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"PartiesInfoLease352AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationLease352AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Lease352AAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Lease352AAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              //Lease 351B
            {"Lease351BBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"PartiesInfoLease351BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationLease351BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Lease351BAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Lease351BAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              //Lease 351C, 1D
            {"Lease351CBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"Lease351DBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"PartiesInfoLease351C1DContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationLease351C1DContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Lease351C1DAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Lease351C1DAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            //Power Of Attorny 48(b)
            {"BasicInfoPOA48bContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BasicPOAInfo" },
            {"PartiesInfoPOA48bContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationPOA48bContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"POA48bAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"POA48bAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // Mortage 
            {"BasicInfoMortgageContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
            {"PartiesInfoMortgageContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMortgageContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MortgageAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MortgageAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // Partnership 
            {"PartnershipBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartnershipBasicInfo" },
            {"PartiesInfoPartnershipContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationPartnershipContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"PartnershipAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"PartnershipAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // LEASE - 35(1(a)) 
            {"LeaseBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"PartiesInfoLeaseContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationLeaseContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"LeaseAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"LeaseAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
             //    RE-CONVEYANCE OF MORTGAGED PROPERTY–54 (All Flows Are same)
            {"BasicInfoMortgagePropertyContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
            {"PartiesInfoMortgagePropertyContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMortgagePropertyContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MortgagePropertyAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MortgagePropertyAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Mortage Crop 41-(A) 
            {"BasicInfoMortgageCropContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
            {"PartiesInfoMortgageCropContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMortgageCropContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MortgageCropAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MortgageCropAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              // Mortage Crop 41-(B)  Same as 41-(A) even headings are same.

            //MORTGAGE-DEED - 40(d)(i)
            {"BasicInfoMortgage40DiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
            //PartiesInfoMortgageContainer
           // PartiesVerificationMortgageContainer
            {"MortgageAgreement40DContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MortgageAgreementPreview40DContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            // MORTGAGE-DEED - 40(d)(ii)
            {"BasicInfoMortgage40DiiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
             


            //   MORTGAGE-DEED - 40(a)
            {"BasicInfoMortgage40AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
          
            //   MORTGAGE-DEED - 40(b)
            {"BasicInfoMortgage40BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
          
          
           // MORTGAGE-DEED - 40(c)
            {"BasicInfoMortgage40CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageBasicInfo" },
            {"MortgageAgreement40CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MortgageAgreementPreview40CContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },


              // Conveyance 23
            {"Conveyance23BasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Conveyance23" },
            {"PartiesInfoConveyance23Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationConveyance23Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Conveyance23AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Conveyance23AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            {"Conveyance23FardDeleteFormComponent","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Conveyance23.Form" },

             // Conveyance 23 - REIT
            {"Conveyance23REITBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Conveyance23" },
            {"PartiesInfoConveyance23REITContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationConveyance23REITContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Conveyance23REITAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },


             // Certificate Of Sale 18
            {"CertificateOfSaleBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.CertificateOfSale" },
            {"PartiesInfoCertificateOfSale18Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationCertificateOfSale18Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"CertificateOfSale18AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"CertificateOfSale18AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            
              // Bond Transfer 6b
            {"BasicInfoTransferContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BondTransferBasicInfo" },
            {"PartiesInfoTransferContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationTransferContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"TransferAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"TransferAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              // Bond Transfer 6a

            {"BasicInfoTransferShareContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BondTransferBasicInfo" },
            {"PartiesInfoTransferShareContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationTransferShareContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"TransferShareAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"TransferShareAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Bill of Exhange 

            {"BasicInfoBillOfExchangeContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BillOfExchangeBasicInfo" },
            {"PartiesInfoBillOfExchangeContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBillOfExchangeContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BillOfExchangeAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BillOfExchangeAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Bill of Exhange 13A2

            {"BasicInfoBillOfExchange13A2Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BillOfExchangeBasicInfo" },
            {"PartiesInfoBillOfExchange13A2Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBillOfExchange13A2Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BillOfExchange13A2AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BillOfExchange13A2AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Bill of Exhange 13B1

            {"BasicInfoBillOfExchange13B1Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BillOfExchangeBasicInfo" },
            {"PartiesInfoBillOfExchange13B1Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBillOfExchange13B1Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BillOfExchange13B1AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BillOfExchange13B1AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Bill of Exhange 13B2

            {"BasicInfoBillOfExchange13B2Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BillOfExchangeBasicInfo" },
            {"PartiesInfoBillOfExchange13B2Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBillOfExchange13B2Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BillOfExchange13B2AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BillOfExchange13B2AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Bill of Exhange 13B3

            {"BasicInfoBillOfExchange13B3Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BillOfExchangeBasicInfo" },
            {"PartiesInfoBillOfExchange13B3Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBillOfExchange13B3Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BillOfExchange13B3AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BillOfExchange13B3AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Award 

            {"BasicInfoAwardContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AwardBasicInfo" },
            {"PartiesInfoAwardContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationAwardContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"AwardAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"AwardAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Partition -45

            {"BasicInfoPartitionContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartitionBasicInfo" },
            {"PartiesInfoPartitionContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationPartitionContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"PartitionAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"PartitionAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // Partition -45d 

            {"BasicInfoPartition45DContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartitionBasicInfo" },
            // Partition -45 Exception

            {"BasicInfoPartition45ExceptionContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartitionBasicInfo" },
           
             // Bond

            {"BasicInfoBondContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BondBasicInfo" },
            {"PartiesInfoBondContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBondContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BondAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BondAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Cancellation

            {"BasicInfoCancellationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.CancellationBasicInfo" },
            {"PartiesInfoCancellationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationCancellationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"CancellationAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"CancellationAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // BottomryBond

            {"BasicInfoBottomryBondContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.BottomryBondBasicInfo" },
            {"PartiesInfoBottomryBondContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationBottomryBondContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"BottomryBondAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"BottomryBondAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Authenicated Declaration -11(b)

            {"BasicInfoAuthenticatedDeclarationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AuthenticatedDeclarationBasicInfo" },
            {"PartiesInfoAuthenticatedDeclarationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationAuthenticatedDeclarationContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"AuthenticatedDeclarationAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"AuthenticatedDeclarationAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
             
            // Shared Warrent 59

            {"ShareWarrentsBasicInfo","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ShareWarrentsBasicInfo" },
            {"PartiesInfoShareWarrent59Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationShareWarrent59Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"ShareWarrent59AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"ShareWarrent59AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             // Further Charge 32A

            {"BasicInfoFurtherCharge32AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.FurtherCharge" },
            {"PartiesInfoFurtherCharge32AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationFurtherCharge32AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"FurtherCharge32AAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"FurtherCharge32AAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            
            // Further Charge 32Bi

            {"BasicInfoFurtherCharge32BiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.FurtherCharge" },
            {"PartiesInfoFurtherCharge32BiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationFurtherCharge32BiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"FurtherCharge32BiAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"FurtherCharge32BiAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // Promissory Note 49A3

            {"BasicInfoPromissoryNote49A3Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PromissoryNote" },
            {"PartiesInfoPromissoryNote49A3Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationPromissoryNote49A3Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"PromissoryNote49A3AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"PromissoryNote49A3AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // Promissory Note 49B

            {"BasicInfoPromissoryNote49BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PromissoryNote" },
            {"PartiesInfoPromissoryNote49BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationPromissoryNote49BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"PromissoryNote49BAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"PromissoryNote49BAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            

            // Rectification159

            {"BasicInfoRectification159Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Rectification" },
            {"PartiesInfoRectification159Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationRectification159Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Rectification159AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Rectification159AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            
            // Note Or Memorandum 43B

            {"BasicInfoNoteOrMemorandum43BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.NoteOrMemorandum" },
            {"PartiesInfoNoteOrMemorandum43BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationNoteOrMemorandum43BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"NoteOrMemorandum43BAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"NoteOrMemorandum43BAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
            
            // Debenture Or Participation Term

            {"BasicInfoDebentureOrParticipationTermContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.DebentureOrParticipationTerm" },
            {"PartiesInfoDebentureOrParticipationTermContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationDebentureOrParticipationTermContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"DebentureOrParticipationTermAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"DebentureOrParticipationTermAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              //ExchangeOfImmovableProperty31
            {"BasicInfoExchangeProperty31Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ImmovablePropertyExchange31" },
            {"PartiesInfoExchangeProperty31Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationExchangeProperty31Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"ExchangeProperty31AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"ExchangeProperty31AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

                //LeaseTransfer 63, //TRANSFER OF LEASE 63
            {"LeaseTransfer63BasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"PartiesInfoLeaseTransfer63Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationLeaseTransfer63Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"LeaseTransfer63AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"LeaseTransfer63AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

               // Transfer of Right or Interest Relating to an Immovable Property-63A
            {"PartiesInfoImmovableProperty63AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationImmovableProperty63AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"ImmovableProperty63AAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"ImmovableProperty63AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

                 //Release 55a and Release55 b
            {"Release55ABasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ReleaseProperty" },
            {"PartiesInfoRelease55AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationRelease55AContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Release55AAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Release55AAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              //Settlement 58A i,ii,iii
            {"Settlement58AiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Settlements" },
            {"Settlement58AiiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Settlements" },
            {"Settlement58AiiiContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Settlements" },
            {"PartiesInfoSettlementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationSettlementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"SettlementAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"SettlementAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

              //Surrender Of Lease 61B
            {"SurrenderOfLease61BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.SurrenderOfLease" },
            {"PartiesInfoSurrenderOfLease61BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationSurrenderOfLease61BContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"SurrenderOfLease61BAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"SurrenderOfLease61BAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Divorce29
            {"BasicInfoDivorce29Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Divorce29BasicInfo" },
            {"PartiesInfoDivorce29Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationDivorce29Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Divorce29AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Divorce29AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Mortgage for housing finance (iv)
            {"MortgageforHousingFinanceivBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.MortgageforHousingFinanceivBasicInfo" },
            {"PartiesInfoMortgageHousingFinanceivContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationMortgageHousingFinanceivContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"MortgageHousingFinanceivAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"MortgageHousingFinanceivAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

             //Respondentia Bond - 56
            {"BasicInfoRespondentiaBond56Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.RespondentiaBond56BasicInfo" },
            {"PartiesInfoRespondentiaBond56Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationRespondentiaBond56Container","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"RespondentiaBond56AgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"RespondentiaBond56AgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            
             //Transfer - 62 b
            {"Transfer62bBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Transfer62bBasicInfo" },
            {"PartiesInfoTransfer62bShareContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationTransfer62bContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"Transfer62bAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"Transfer62bAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },

            // AGREEMENT RELATING TO DEPOSIT OF TITLE DEEDS, PAWN OR PLEDGE - 6(a)(i)
            {"DeedAgreement6AiBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            {"PartiesInfoDeedAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo" },
            {"PartiesVerificationDeedAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification" },
            {"DeedAgreementAgreementContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.Agreements" },
            {"DeedAgreementAgreementPreviewContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.AgreementPreview" },
             // AGREEMENT RELATING TO DEPOSIT OF TITLE DEEDS, PAWN OR PLEDGE - 6(a)(ii)
            {"DeedAgreement6AiiBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            
              // AGREEMENT RELATING TO DEPOSIT OF TITLE DEEDS, PAWN OR PLEDGE - 6(b)(i)
            {"DeedAgreement6BiBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            
             // AGREEMENT RELATING TO DEPOSIT OF TITLE DEEDS, PAWN OR PLEDGE - 6(b)(ii)
            {"DeedAgreement6BiiBasicInfoContainer","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.LeaseBasicInfo" },
            

            //Misc Components
            { "CameraPictureFormComponent","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification.Form"},
            { "BiometricFormComponent","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesVerification.Form"},
            { "PartiesInfoDeleteFormComponent","PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo.Form"},
            { "ChallanTemplateListingComponent", "PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PaymentProcessors.Listing"},
            { "MessageDialogComponent", "PLRA.ESORS.Portal.Razor.Components"}
        };
    }
}
