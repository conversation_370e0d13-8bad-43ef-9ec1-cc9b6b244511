using PLRA.ESORS.Server.Data.Entities;

namespace PLRA.ESORS.ServiceContracts
{
    public interface IErrorLoggingService
    {
        /// <summary>
        /// Logs an error with full context information
        /// </summary>
        Task LogErrorAsync(string componentName, string methodName, Exception exception, object? additionalData = null);

        /// <summary>
        /// Logs an error with custom message and context
        /// </summary>
        Task LogErrorAsync(string componentName, string methodName, string errorMessage, string? stackTrace = null, object? additionalData = null);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        Task LogWarningAsync(string componentName, string methodName, string message, object? additionalData = null);

        /// <summary>
        /// Logs an informational message
        /// </summary>
        Task LogInformationAsync(string componentName, string methodName, string message, object? additionalData = null);

        /// <summary>
        /// Logs a critical error that requires immediate attention
        /// </summary>
        Task LogCriticalAsync(string componentName, string methodName, Exception exception, object? additionalData = null);

        /// <summary>
        /// Logs a critical error with custom message
        /// </summary>
        Task LogCriticalAsync(string componentName, string methodName, string errorMessage, string? stackTrace = null, object? additionalData = null);

        /// <summary>
        /// Logs a database operation error with specific context
        /// </summary>
        Task LogDatabaseErrorAsync(string componentName, string methodName, Exception exception, string? sqlQuery = null, object? parameters = null);

        /// <summary>
        /// Logs an API call error with request/response context
        /// </summary>
        Task LogApiErrorAsync(string componentName, string methodName, Exception exception, string? requestUrl = null, string? requestBody = null, string? responseBody = null);

        /// <summary>
        /// Logs a validation error with field-specific information
        /// </summary>
        Task LogValidationErrorAsync(string componentName, string methodName, string validationMessage, object? invalidData = null);

        /// <summary>
        /// Logs an authentication/authorization error
        /// </summary>
        Task LogAuthenticationErrorAsync(string componentName, string methodName, string errorMessage, string? userId = null, string? attemptedAction = null);

        /// <summary>
        /// Logs a file operation error
        /// </summary>
        Task LogFileOperationErrorAsync(string componentName, string methodName, Exception exception, string? filePath = null, string? operation = null);

        /// <summary>
        /// Logs an external service integration error
        /// </summary>
        Task LogExternalServiceErrorAsync(string componentName, string methodName, Exception exception, string? serviceName = null, string? serviceEndpoint = null);

        /// <summary>
        /// Logs a workflow-specific error with task context
        /// </summary>
        Task LogWorkflowErrorAsync(string componentName, string methodName, Exception exception, long? taskId = null, long? workflowId = null, string? workflowStep = null);

        /// <summary>
        /// Retrieves error logs with filtering and pagination
        /// </summary>
        Task<IEnumerable<ErrorLog>> GetErrorLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? severityLevel = null, string? componentName = null, int skip = 0, int take = 100);

        /// <summary>
        /// Marks an error as resolved
        /// </summary>
        Task MarkErrorAsResolvedAsync(long errorLogId, string resolutionNotes);

        /// <summary>
        /// Gets error statistics for dashboard/monitoring
        /// </summary>
        Task<ErrorLogStatistics> GetErrorStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }

    public class ErrorLogStatistics
    {
        public int TotalErrors { get; set; }
        public int CriticalErrors { get; set; }
        public int Warnings { get; set; }
        public int UnresolvedErrors { get; set; }
        public Dictionary<string, int> ErrorsByComponent { get; set; } = new();
        public Dictionary<string, int> ErrorsBySeverity { get; set; } = new();
        public Dictionary<DateTime, int> ErrorsByDate { get; set; } = new();
    }

    public static class ErrorSeverityLevels
    {
        public const string Critical = "Critical";
        public const string Error = "Error";
        public const string Warning = "Warning";
        public const string Information = "Information";
    }

    public static class ErrorCategories
    {
        public const string Database = "Database";
        public const string Api = "API";
        public const string Validation = "Validation";
        public const string Authentication = "Authentication";
        public const string FileOperation = "FileOperation";
        public const string ExternalService = "ExternalService";
        public const string Workflow = "Workflow";
        public const string General = "General";
    }
}
