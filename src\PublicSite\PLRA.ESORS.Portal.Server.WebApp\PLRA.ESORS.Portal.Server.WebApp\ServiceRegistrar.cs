using Microsoft.AspNetCore.Authentication;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.DataProtections;
using PLRA.ESORS.Framework.UIServices;
using PLRA.ESORS.Portal.Server.DataServices;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementMemorandumBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementMemorandumBasicInfo5CC;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmission;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmission5CC;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmissionCertificateOfSale18;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmissionGift33;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmissionLease351B;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmissionLease351C1D;
using PLRA.ESORS.Portal.Server.DataServices.Features.AgreementSubmissionPOA48b;
using PLRA.ESORS.Portal.Server.DataServices.Features.ArticlesOfClerkshipInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.AuthenticatedDeclarationBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.AwardBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BasicPOA48bInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BasicPOAInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BillOfExchange13A2BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BillOfExchange13B1BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BillOfExchange13B2BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BillOfExchange13B3BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BillOfExchangeBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.Biometric;
using PLRA.ESORS.Portal.Server.DataServices.Features.BondBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BondTransferBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BottomryBondBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.BreadCrumb;
using PLRA.ESORS.Portal.Server.DataServices.Features.CameraPicture;
using PLRA.ESORS.Portal.Server.DataServices.Features.CancellationBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.CertificateOfSale18;
using PLRA.ESORS.Portal.Server.DataServices.Features.ChallanTemplate;
using PLRA.ESORS.Portal.Server.DataServices.Features.CompanyPartyInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.CompanySecondPartyInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFard;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFard5CC;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFardCertificateOfSale18;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFardGift33;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFardLease351B;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFardLease351C1D;
using PLRA.ESORS.Portal.Server.DataServices.Features.ComputerizedFardPOA48b;
using PLRA.ESORS.Portal.Server.DataServices.Features.ContractDetailInformation;
using PLRA.ESORS.Portal.Server.DataServices.Features.Conveyance23BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.Conveyance23FardDelete;
using PLRA.ESORS.Portal.Server.DataServices.Features.CourtFeeAgreement;
using PLRA.ESORS.Portal.Server.DataServices.Features.DebentureOrParticipationTermBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.Decree27A;
using PLRA.ESORS.Portal.Server.DataServices.Features.DeedAgreement;
using PLRA.ESORS.Portal.Server.DataServices.Features.Divorce29BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.FurtherCharge32ABasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.FurtherCharge32BiBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.FurtherCharge32BiiBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.Gift33BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.ImmovablePropertyExchange31;
using PLRA.ESORS.Portal.Server.DataServices.Features.Inbox;
using PLRA.ESORS.Portal.Server.DataServices.Features.InboxStats;
using PLRA.ESORS.Portal.Server.DataServices.Features.Lease351BBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.Lease351C1DBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.LeaseBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFard;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFard5CC;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardCertificateOfSale18;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardGift33;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardLease351B;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardLease351C1D;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardPOA48b;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRural;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRural5CC;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRuralCertificateOfSale18;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRuralGift33;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRuralLease351B;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRuralLease351C1D;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardRuralPOA48b;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrban;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrban5CC;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrbanCertificateOfSale18;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrbanGift33;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrbanLease351B;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrbanLease351C1D;
using PLRA.ESORS.Portal.Server.DataServices.Features.ManualFardUrbanPOA48b;
using PLRA.ESORS.Portal.Server.DataServices.Features.MortgageBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.MortgageforHousingFinanceivBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.NoteOrMemorandum43B;
using PLRA.ESORS.Portal.Server.DataServices.Features.PartiesInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.PartiesInfoDelete;
using PLRA.ESORS.Portal.Server.DataServices.Features.PartiesVerification;
using PLRA.ESORS.Portal.Server.DataServices.Features.PartitionBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.PartnershipBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.PaymentProcessor;
using PLRA.ESORS.Portal.Server.DataServices.Features.PaymentVerification;
using PLRA.ESORS.Portal.Server.DataServices.Features.PromissoryNote49A3BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.PromissoryNote49BBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.PropertyBasicPoaInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.PropertyDetail;
using PLRA.ESORS.Portal.Server.DataServices.Features.Rectification159BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.ReleaseProperty;
using PLRA.ESORS.Portal.Server.DataServices.Features.RespondentiaBond56BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.SearchMain;
using PLRA.ESORS.Portal.Server.DataServices.Features.SearchSub;
using PLRA.ESORS.Portal.Server.DataServices.Features.SearchSubDetail;
using PLRA.ESORS.Portal.Server.DataServices.Features.Settlement58;
using PLRA.ESORS.Portal.Server.DataServices.Features.ShareWarrent59BasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.StampInformation;
using PLRA.ESORS.Portal.Server.DataServices.Features.StepperForm;
using PLRA.ESORS.Portal.Server.DataServices.Features.StepperFormVerification;
using PLRA.ESORS.Portal.Server.DataServices.Features.SurrenderOfLease61B;
using PLRA.ESORS.Portal.Server.DataServices.Features.TaskCreation;
using PLRA.ESORS.Portal.Server.DataServices.Features.Transfer62bBasicInfo;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.AdditionalNotes.Form;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.AdditionalNotes.Listing;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.BasicCourtInfo.Form;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.BasicCourtInfo.Listing;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.BasicPOAInfo.Listing;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.ContractDetailInfo.Listing;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.FeeDetail.Listing;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkFlows.Components.PartiesInfo.Listing;
using PLRA.ESORS.Portal.Server.DataServices.Features.WorkflowStepper;
using PLRA.ESORS.Portal.Server.DataServices.Recaptcha;
using PLRA.ESORS.Portal.Server.DataServices.SelectList;
using PLRA.ESORS.Portal.Server.DataServices.Services;
using PLRA.ESORS.Portal.Server.DataServices.Services.EpayApiService;
using PLRA.ESORS.Portal.Server.DataServices.Services.FardService;
using PLRA.ESORS.Portal.Server.DataServices.Services.FBRApiService;
using PLRA.ESORS.Portal.Server.DataServices.Services.NadraService;
using PLRA.ESORS.Portal.Server.DataServices.Services.PaymentService;
using PLRA.ESORS.Portal.Server.DataServices.Services.TerritoryService;
using PLRA.ESORS.Portal.ServiceContracts;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementMemorandumBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementMemorandumBasicInfo5CC;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmission;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmission5CC;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmissionCertificateOfSale18;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmissionGift33;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmissionLease351B;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmissionLease351C1D;
using PLRA.ESORS.Portal.ServiceContracts.Features.AgreementSubmissionPOA48b;
using PLRA.ESORS.Portal.ServiceContracts.Features.ArticlesOfClerkshipInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.AuthenticatedDeclarationBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.AwardBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BasicPOA48bInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BasicPOAInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BillOfExchange13A2BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BillOfExchange13B1BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BillOfExchange13B2BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BillOfExchange13B3BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BillOfExchangeBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.Biometric;
using PLRA.ESORS.Portal.ServiceContracts.Features.BondBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BondTransferBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BottomryBondBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.BreadCrumb;
using PLRA.ESORS.Portal.ServiceContracts.Features.CameraPicture;
using PLRA.ESORS.Portal.ServiceContracts.Features.CancellationBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.CertificateOfSale18;
using PLRA.ESORS.Portal.ServiceContracts.Features.ChallanTemplate;
using PLRA.ESORS.Portal.ServiceContracts.Features.CompanyPartyInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.CompanySecondPartyInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFard;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFard5CC;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFardCertificateOfSale18;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFardGift33;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFardLease351B;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFardLease351C1D;
using PLRA.ESORS.Portal.ServiceContracts.Features.ComputerizedFardPOA48b;
using PLRA.ESORS.Portal.ServiceContracts.Features.ContractDetailInformation;
using PLRA.ESORS.Portal.ServiceContracts.Features.Conveyance23BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.Conveyance23FardDelete;
using PLRA.ESORS.Portal.ServiceContracts.Features.CourtFeeAgreement;
using PLRA.ESORS.Portal.ServiceContracts.Features.Dashboard;
using PLRA.ESORS.Portal.ServiceContracts.Features.DebentureOrParticipationTermBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.Decree27A;
using PLRA.ESORS.Portal.ServiceContracts.Features.DeedAgreement;
using PLRA.ESORS.Portal.ServiceContracts.Features.Divorce29BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.FurtherCharge32ABasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.FurtherCharge32BiBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.FurtherCharge32BiiBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.Gift33BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.ImmovablePropertyExchange31;
using PLRA.ESORS.Portal.ServiceContracts.Features.Inbox;
using PLRA.ESORS.Portal.ServiceContracts.Features.InboxStats;
using PLRA.ESORS.Portal.ServiceContracts.Features.Lease351BBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.Lease351C1DBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.LeaseBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFard;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFard5CC;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardCertificateOfSale18;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardGift33;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardLease351B;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardLease351C1D;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardPOA48b;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRural;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRural5CC;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRuralCertificateOfSale18;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRuralGift33;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRuralLease351B;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRuralLease351C1D;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardRuralPOA48b;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrban;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrban5CC;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrbanCertificateOfSale18;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrbanGift33;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrbanLease351B;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrbanLease351C1D;
using PLRA.ESORS.Portal.ServiceContracts.Features.ManualFardUrbanPOA48b;
using PLRA.ESORS.Portal.ServiceContracts.Features.MortgageBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.MortgageforHousingFinanceivBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.NoteOrMemorandum43B;
using PLRA.ESORS.Portal.ServiceContracts.Features.PartiesInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.PartiesInfoDelete;
using PLRA.ESORS.Portal.ServiceContracts.Features.PartiesVerification;
using PLRA.ESORS.Portal.ServiceContracts.Features.PartitionBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.PartnershipBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.PaymentProcessor;
using PLRA.ESORS.Portal.ServiceContracts.Features.PaymentVerification;
using PLRA.ESORS.Portal.ServiceContracts.Features.PromissoryNote49A3BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.PromissoryNote49BBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.PropertyBasicPoaInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.PropertyDetail;
using PLRA.ESORS.Portal.ServiceContracts.Features.Rectification159BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.ReleaseProperty;
using PLRA.ESORS.Portal.ServiceContracts.Features.RespondentiaBond56BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.SearchMain;
using PLRA.ESORS.Portal.ServiceContracts.Features.SearchSub;
using PLRA.ESORS.Portal.ServiceContracts.Features.SearchSubDetail;
using PLRA.ESORS.Portal.ServiceContracts.Features.Settlement58;
using PLRA.ESORS.Portal.ServiceContracts.Features.ShareWarrent59BasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.StampInformation;
using PLRA.ESORS.Portal.ServiceContracts.Features.StepperForm;
using PLRA.ESORS.Portal.ServiceContracts.Features.StepperFormVerification;
using PLRA.ESORS.Portal.ServiceContracts.Features.SurrenderOfLease61B;
using PLRA.ESORS.Portal.ServiceContracts.Features.TaskCreation;
using PLRA.ESORS.Portal.ServiceContracts.Features.Transfer62bBasicInfo;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.AdditionalNotes.Form;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.AdditionalNotes.Listing;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.BasicCourtInfo.Form;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.BasicCourtInfo.Listing;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.BasicPOAInfo.Listing;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.ContractDetailInfo.Listing;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.FeeDetail.Listing;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkFlows.Components.PartiesInfo.Listing;
using PLRA.ESORS.Portal.ServiceContracts.Features.WorkflowStepper;
using PLRA.ESORS.Portal.ServiceContracts.SelectList;
using PLRA.ESORS.Portal.ServiceContracts;
using PLRA.ESORS.Portal.Server.DataServices.Services.ErrorLogging;
//##NewServiceNamespace##

namespace PLRA.ESORS.Portal.Server.WebApp
{
    public static class ServiceRegistrar
    {
        public static void RegisterServices(this IServiceCollection services, ConfigurationManager configuration)
        {


            services.Configure<ResourceSettings>(configuration.GetSection("ResourceSettings"));
            services.Configure<NadraSettings>(configuration.GetSection("NadraSettings"));
            services.Configure<FardSettings>(configuration.GetSection("FardSettings"));
            services.Configure<TerritorySettings>(configuration.GetSection("TerritorySettings"));
            services.Configure<EpayApiSettings>(configuration.GetSection("EpayApiSettings"));
            services.Configure<SecureDataSetting>(configuration.GetSection("SecureDataSetting"));
            services.Configure<PersonSettings>(configuration.GetSection("PersonSettings"));
            services.Configure<FBRApiSettings>(configuration.GetSection("FBRApiSettings"));

            services.AddHttpClient<INadraApiService, NadraApiService>();
            services.AddHttpClient<IFardService, FardService>();
            services.AddHttpClient<IStaticTerritoryService, StaticTerritoryService>();
            services.AddHttpClient<IEpayApiService, EpayApiService>();

            services.AddSingleton<IDataProtection, DataProtection>();
            services.AddSingleton<CaptchaService>();
            services.AddSingleton<AlertService>();
            services.AddScoped<KtDialogService>();
            services.AddScoped<KtNotificationService>();
            services.AddScoped<IMessageCenter, MessageCenter>();
            services.AddScoped<IClaimsTransformation, CustomClaimsTransformation>();
            services.AddScoped<IAuthenticatedUser, ServerAuthenticatedUser>();
            services.AddScoped<ILocalStorageService, LocalStorageService>();
            services.AddScoped<ISelectListDataService, SelectListDataService>();
            services.AddScoped<IErrorLoggingService, ErrorLoggingServerSideDataService>();
            services.AddScoped<IStaticTerritoryService, StaticTerritoryService>();
            services.AddScoped<INadraApiService, NadraApiService>();
            services.AddScoped<INadraBiometricApiService, NadraBiometricApiService>();
            services.AddScoped<IFBRApiService, FBRApiService>();

            services.AddScoped<PaymentsRuleEngineService>();
            services.AddScoped<IPaymentService, PaymentService>();
            services.AddScoped<IPaymentDataFetchingService, PaymentDataFetchingService>();
            services.AddScoped<IWorkflowEntityMappingService, WorkflowEntityMappingService>();

            services.AddScoped<IDashboardListingDataService, DashboardServerSideListingDataService>();
            services.AddScoped<ISearchMainListingDataService, SearchMainServerSideListingDataService>();
            services.AddScoped<ISearchSubListingDataService, SearchSubServerSideListingDataService>();
            services.AddScoped<ISearchSubDetailListingDataService, SearchSubDetailServerSideListingDataService>();
            services.AddScoped<ITaskCreationFormDataService, TaskCreationServerSideFormDataService>();
            services.AddScoped<IWorkflowStepperListingDataService, WorkflowStepperServerSideListingDataService>();
            services.AddScoped<ISearchMainListingDataService, SearchMainServerSideListingDataService>();
            services.AddScoped<ISearchSubListingDataService, SearchSubServerSideListingDataService>();
            services.AddScoped<ISearchSubDetailListingDataService, SearchSubDetailServerSideListingDataService>();
            services.AddScoped<IBasicCourtInformationFormDataService, BasicCourtInformationServerSideFormDataService>();
            services.AddScoped<IGenerateAndVerifyChallansListingDataService, GenerateAndVerifyChallansServerSideListingDataService>();
            services.AddScoped<IPartiesVerificationListingDataService, PartiesVerificationServerSideListingDataService>();
            services.AddScoped<IVerificationFormDataService, VerificationServerSideFormDataService>();
            services.AddScoped<IBasicCourtInfoListingDataService, BasicCourtInfoServerSideListingDataService>();
            services.AddScoped<IPartiesInfoListingDataService, PartiesInfoServerSideListingDataService>();
            services.AddScoped<IAdditionalNotesFormDataService, AdditionalNotesServerSideFormDataService>();
            services.AddScoped<IGenerateAndVerifyChallansListingDataService, GenerateAndVerifyChallansServerSideListingDataService>();
            services.AddScoped<ISearchMainListingDataService, SearchMainServerSideListingDataService>();
            services.AddScoped<ISearchSubListingDataService, SearchSubServerSideListingDataService>();
            services.AddScoped<ISearchSubDetailListingDataService, SearchSubDetailServerSideListingDataService>();
            services.AddScoped<IBasicCourtInformationFormDataService, BasicCourtInformationServerSideFormDataService>();
            services.AddScoped<IPartiesInfoFormDataService, PartiesInfoServerSideFormDataService>();
            services.AddScoped<IPartiesInfoListingDataService, PartiesInfoServerSideListingDataService>();
            services.AddScoped<IAdditionalNotesListingDataService, AdditionalNotesServerSideListingDataService>();
            services.AddScoped<IStepperFormFormDataService, StepperFormServerSideFormDataService>();
            services.AddScoped<IPartiesInfoRespondentFormDataService, PartiesInfoRespondentServerSideFormDataService>();
            services.AddScoped<IPartiesInfoAttorneyFormDataService, PartiesInfoAttorneyServerSideFormDataService>();
            services.AddScoped<IStampInfoFormDataService, StampInfoServerSideFormDataService>();
            services.AddScoped<IStampInfoListingDataService, StampInfoServerSideListingDataService>();
            services.AddScoped<IStampInfoDeleteFormDataService, StampInfoDeleteServerSideFormDataService>();
            services.AddScoped<IContractDetailInformationFormDataService, ContractDetailInformationServerSideFormDataService>();
            services.AddScoped<ICompanyFirstPartyInfoFormDataService, CompanyFirstPartyInfoServerSideFormDataService>();
            services.AddScoped<ICompanyPartyInfoListingDataService, CompanyPartyInfoServerSideListingDataService>();
            services.AddScoped<ICompanySecondPartyInfoFormDataService, CompanySecondPartyInfoServerSideFormDataService>();
            services.AddScoped<IBasicPoaInfoFormDataService, BasicPoaInfoServerSideFormDataService>();
            services.AddScoped<IBasicPoaInfoListingDataService, BasicPoaInfoServerSideListingDataService>();
            services.AddScoped<ICompanySecondPartyInfoFormDataService, CompanySecondPartyInfoServerSideFormDataService>();
            services.AddScoped<IContractDetailInfoListingDataService, ContractDetailInfoServerSideListingDataService>();
            services.AddScoped<IFeeDetailListingDataService, FeeDetailServerSideListingDataService>();
            services.AddScoped<IPartiesInfoDeleteFormDataService, PartiesInfoDeleteServerSideFormDataService>();
            services.AddScoped<ICameraPictureFormDataService, CameraPictureServerSideFormDataService>();
            services.AddScoped<IBiometricFormDataService, BiometricServerSideFormDataService>();

            services.AddScoped<IPropertyBasicPoaInfoFormDataService, PropertyBasicPoaInfoServerSideFormDataService>();
            services.AddScoped<IPropertyBasicPoaInfoListingDataService, PropertyBasicPoaInfoServerSideListingDataService>();
            services.AddScoped<IArticlesOfClerkshipInfoFormDataService, ArticlesOfClerkshipInfoServerSideFormDataService>();
            services.AddScoped<IArticlesOfClerkshipInfoListingDataService, ArticlesOfClerkshipInfoServerSideListingDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfoCCFormDataService, AgreementMemorandumBasicInfoCCServerSideFormDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfoCCListingDataService, AgreementMemorandumBasicInfoCCServerSideListingDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfoBFormDataService, AgreementMemorandumBasicInfoBServerSideFormDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfoBListingDataService, AgreementMemorandumBasicInfoBServerSideListingDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfoCFormDataService, AgreementMemorandumBasicInfoCServerSideFormDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfoCListingDataService, AgreementMemorandumBasicInfoCServerSideListingDataService>();
            services.AddScoped<IMortgageBasicInfoFormDataService, MortgageBasicInfoServerSideFormDataService>();
            services.AddScoped<IMortgageBasicInfoListingDataService, MortgageBasicInfoServerSideListingDataService>();
            services.AddScoped<IPartnershipBasicInfoFormDataService, PartnershipBasicInfoServerSideFormDataService>();
            services.AddScoped<IPartnershipBasicInfoListingDataService, PartnershipBasicInfoServerSideListingDataService>();
            services.AddScoped<ILeaseBasicInfoFormDataService, LeaseBasicInfoServerSideFormDataService>();
            services.AddScoped<ILeaseBasicInfoListingDataService, LeaseBasicInfoServerSideListingDataService>();
            services.AddScoped<IMortgagePropertyBasicInfoFormDataService, MortgagePropertyBasicInfoServerSideFormDataService>();
            services.AddScoped<IMortgagePropertyBasicInfoListingDataService, MortgagePropertyBasicInfoServerSideListingDataService>();
            services.AddScoped<IMortgageCropBasicInfoFormDataService, MortgageCropBasicInfoServerSideFormDataService>();
            services.AddScoped<IMortgageCropBasicInfoListingDataService, MortgageCropBasicInfoServerSideListingDataService>();
            services.AddScoped<IConveyance23BasicInfoFormDataService, Conveyance23BasicInfoServerSideFormDataService>();
            services.AddScoped<IConveyance23BasicInfoListingDataService, Conveyance23BasicInfoServerSideListingDataService>();
            services.AddScoped<IComputerizedFardListingDataService, ComputerizedFardServerSideListingDataService>();
            services.AddScoped<IManualFardRuralFormDataService, ManualFardRuralServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanFormDataService, ManualFardUrbanServerSideFormDataService>();
            services.AddScoped<IManualFardListingDataService, ManualFardServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionFormDataService, AgreementSubmissionServerSideFormDataService>();
            services.AddScoped<IBondTransferBasicInfoFormDataService, BondTransferBasicInfoServerSideFormDataService>();
            services.AddScoped<IBondTransferBasicInfoListingDataService, BondTransferBasicInfoServerSideListingDataService>();
            services.AddScoped<IBondTransferSharesBasicInfoFormDataService, BondTransferSharesBasicInfoServerSideFormDataService>();
            services.AddScoped<IBondTransferSharesBasicInfoListingDataService, BondTransferSharesBasicInfoServerSideListingDataService>();
            services.AddScoped<IBillOfExchangeBasicInfoFormDataService, BillOfExchangeBasicInfoServerSideFormDataService>();
            services.AddScoped<IBillOfExchangeBasicInfoListingDataService, BillOfExchangeBasicInfoServerSideListingDataService>();
            services.AddScoped<IAwardBasicInfoFormDataService, AwardBasicInfoServerSideFormDataService>();
            services.AddScoped<IAwardBasicInfoListingDataService, AwardBasicInfoServerSideListingDataService>();
            services.AddScoped<IPartitionBasicInfoFormDataService, PartitionBasicInfoServerSideFormDataService>();
            services.AddScoped<IPartitionBasicInfoListingDataService, PartitionBasicInfoServerSideListingDataService>();
            services.AddScoped<IBondBasicInfoFormDataService, BondBasicInfoServerSideFormDataService>();
            services.AddScoped<IBondBasicInfoListingDataService, BondBasicInfoServerSideListingDataService>();
            services.AddScoped<ICancellationBasicInfoFormDataService, CancellationBasicInfoServerSideFormDataService>();
            services.AddScoped<ICancellationBasicInfoListingDataService, CancellationBasicInfoServerSideListingDataService>();
            services.AddScoped<IBottomryBondBasicInfoFormDataService, BottomryBondBasicInfoServerSideFormDataService>();
            services.AddScoped<IBottomryBondBasicInfoListingDataService, BottomryBondBasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfo5CCFormDataService, AgreementMemorandumBasicInfo5CCServerSideFormDataService>();
            services.AddScoped<IAgreementMemorandumBasicInfo5CCListingDataService, AgreementMemorandumBasicInfo5CCServerSideListingDataService>();
            services.AddScoped<IGift33BasicInfoFormDataService, Gift33BasicInfoServerSideFormDataService>();
            services.AddScoped<IGift33BasicInfoListingDataService, Gift33BasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionGift33FormDataService, AgreementSubmissionGift33ServerSideFormDataService>();
            services.AddScoped<IAgreementSubmission5CCFormDataService, AgreementSubmission5CCServerSideFormDataService>();
            services.AddScoped<IManualFardRural5CCFormDataService, ManualFardRural5CCServerSideFormDataService>();
            services.AddScoped<IManualFardUrban5CCFormDataService, ManualFardUrban5CCServerSideFormDataService>();
            services.AddScoped<IManualFard5CCListingDataService, ManualFard5CCServerSideListingDataService>();
            services.AddScoped<IComputerizedFard5CCListingDataService, ComputerizedFard5CCServerSideListingDataService>();
            services.AddScoped<IManualFardRuralGift33FormDataService, ManualFardRuralGift33ServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanGift33FormDataService, ManualFardUrbanGift33ServerSideFormDataService>();
            services.AddScoped<IManualFardGift33ListingDataService, ManualFardGift33ServerSideListingDataService>();
            services.AddScoped<IComputerizedFardGift33ListingDataService, ComputerizedFardGift33ServerSideListingDataService>();
            services.AddScoped<IAuthenticatedDeclarationBasicInfoFormDataService, AuthenticatedDeclarationBasicInfoServerSideFormDataService>();
            services.AddScoped<IAuthenticatedDeclarationBasicInfoListingDataService, AuthenticatedDeclarationBasicInfoServerSideListingDataService>();
            services.AddScoped<IManualFardPOA48bListingDataService, ManualFardPOA48bServerSideListingDataService>();
            services.AddScoped<IManualFardRuralPOA48bFormDataService, ManualFardRuralPOA48bServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanPOA48bFormDataService, ManualFardUrbanPOA48bServerSideFormDataService>();
            services.AddScoped<IAgreementSubmissionPOA48bFormDataService, AgreementSubmissionPOA48bServerSideFormDataService>();
            services.AddScoped<IBasicPOA48bInfoFormDataService, BasicPOA48bInfoServerSideFormDataService>();
            services.AddScoped<IBasicPOA48bInfoListingDataService, BasicPOA48bInfoServerSideListingDataService>();
            services.AddScoped<IComputerizedFardPOA48bListingDataService, ComputerizedFardPOA48bServerSideListingDataService>();
            services.AddScoped<IChallanTemplateListingDataService, ChallanTemplateServerSideListingDataService>();
            services.AddScoped<IDecree27ABasicInfoFormDataService, Decree27ABasicInfoServerSideFormDataService>();
            services.AddScoped<IDecree27ABasicInfoListingDataService, Decree27ABasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionDecree27AFormDataService, AgreementSubmissionDecree27AServerSideFormDataService>();
            services.AddScoped<IManualFardRuralDecree27AFormDataService, ManualFardRuralDecree27AServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanDecree27AFormDataService, ManualFardUrbanDecree27AServerSideFormDataService>();
            services.AddScoped<IComputerizedFardDecree27AListingDataService, ComputerizedFardDecree27AServerSideListingDataService>();
            services.AddScoped<IManualFardDecree27AListingDataService, ManualFardDecree27AServerSideListingDataService>();
            services.AddScoped<IFurtherCharge32ABasicInfoFormDataService, FurtherCharge32ABasicInfoServerSideFormDataService>();
            services.AddScoped<IFurtherCharge32ABasicInfoListingDataService, FurtherCharge32ABasicInfoServerSideListingDataService>();
            services.AddScoped<IFurtherCharge32BiBasicInfoFormDataService, FurtherCharge32BiBasicInfoServerSideFormDataService>();
            services.AddScoped<IFurtherCharge32BiBasicInfoListingDataService, FurtherCharge32BiBasicInfoServerSideListingDataService>();
            services.AddScoped<IFurtherCharge32BiiBasicInfoFormDataService, FurtherCharge32BiiBasicInfoServerSideFormDataService>();
            services.AddScoped<IFurtherCharge32BiiBasicInfoListingDataService, FurtherCharge32BiiBasicInfoServerSideListingDataService>();
            services.AddScoped<IPaymentVerificationListingDataService, PaymentVerificationServerSideListingDataService>();
            services.AddScoped<ILease352ABasicInfoFormDataService, Lease352ABasicInfoServerSideFormDataService>();
            services.AddScoped<ILease352ABasicInfoListingDataService, Lease352ABasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionLease352AFormDataService, AgreementSubmissionLease352AServerSideFormDataService>();
            services.AddScoped<IManualFardRuralLease352AFormDataService, ManualFardRuralLease352AServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanLease352AFormDataService, ManualFardUrbanLease352AServerSideFormDataService>();
            services.AddScoped<IComputerizedFardLease352AListingDataService, ComputerizedFardLease352AServerSideListingDataService>();
            services.AddScoped<IManualFardLease352AListingDataService, ManualFardLease352AServerSideListingDataService>();
            services.AddScoped<IShareWarrent59BasicInfoFormDataService, ShareWarrent59BasicInfoServerSideFormDataService>();
            services.AddScoped<IShareWarrent59BasicInfoListingDataService, ShareWarrent59BasicInfoServerSideListingDataService>();
            services.AddScoped<INoteOrMemorandum43BFormDataService, NoteOrMemorandum43BServerSideFormDataService>();
            services.AddScoped<INoteOrMemorandum43BListingDataService, NoteOrMemorandum43BServerSideListingDataService>();
            services.AddScoped<IBillOfExchange13A2BasicInfoFormDataService, BillOfExchange13A2BasicInfoServerSideFormDataService>();
            services.AddScoped<IBillOfExchange13A2BasicInfoListingDataService, BillOfExchange13A2BasicInfoServerSideListingDataService>();
            services.AddScoped<IBillOfExchange13B1BasicInfoFormDataService, BillOfExchange13B1BasicInfoServerSideFormDataService>();
            services.AddScoped<IBillOfExchange13B1BasicInfoListingDataService, BillOfExchange13B1BasicInfoServerSideListingDataService>();
            services.AddScoped<IBillOfExchange13B2BasicInfoFormDataService, BillOfExchange13B2BasicInfoServerSideFormDataService>();
            services.AddScoped<IBillOfExchange13B2BasicInfoListingDataService, BillOfExchange13B2BasicInfoServerSideListingDataService>();
            services.AddScoped<IBillOfExchange13B3BasicInfoFormDataService, BillOfExchange13B3BasicInfoServerSideFormDataService>();
            services.AddScoped<IBillOfExchange13B3BasicInfoListingDataService, BillOfExchange13B3BasicInfoServerSideListingDataService>();
            services.AddScoped<ILease351BBasicInfoFormDataService, Lease351BBasicInfoServerSideFormDataService>();
            services.AddScoped<ILease351BBasicInfoListingDataService, Lease351BBasicInfoServerSideListingDataService>();
            services.AddScoped<IManualFardRuralLease351BFormDataService, ManualFardRuralLease351BServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanLease351BFormDataService, ManualFardUrbanLease351BServerSideFormDataService>();
            services.AddScoped<IAgreementSubmissionLease351BFormDataService, AgreementSubmissionLease351BServerSideFormDataService>();
            services.AddScoped<IComputerizedFardLease351BListingDataService, ComputerizedFardLease351BServerSideListingDataService>();
            services.AddScoped<IManualFardLease351BListingDataService, ManualFardLease351BServerSideListingDataService>();
            services.AddScoped<ILease351C1DBasicInfoFormDataService, Lease351C1DBasicInfoServerSideFormDataService>();
            services.AddScoped<ILease351C1DBasicInfoListingDataService, Lease351C1DBasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionLease351C1DFormDataService, AgreementSubmissionLease351C1DServerSideFormDataService>();
            services.AddScoped<IManualFardRuralLease351C1DFormDataService, ManualFardRuralLease351C1DServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanLease351C1DFormDataService, ManualFardUrbanLease351C1DServerSideFormDataService>();
            services.AddScoped<IComputerizedFardLease351C1DListingDataService, ComputerizedFardLease351C1DServerSideListingDataService>();
            services.AddScoped<IManualFardLease351C1DListingDataService, ManualFardLease351C1DServerSideListingDataService>();
            services.AddScoped<IMortgageBasicInfo40DiFormDataService, MortgageBasicInfo40DiServerSideFormDataService>();
            services.AddScoped<IMortgageBasicInfo40DiListingDataService, MortgageBasicInfo40DiServerSideListingDataService>();
            services.AddScoped<IMortgageBasicInfo40CFormDataService, MortgageBasicInfo40CServerSideFormDataService>();
            services.AddScoped<IMortgageBasicInfo40CListingDataService, MortgageBasicInfo40CServerSideListingDataService>();
            services.AddScoped<ICourtFeeAgreementListingDataService, CourtFeeAgreementServerSideListingDataService>();
            services.AddScoped<IBasicExchangeProperty31InfoFormDataService, BasicExchangeProperty31InfoServerSideFormDataService>();
            services.AddScoped<IBasicExchangeProperty31InfoListingDataService, BasicExchangeProperty31InfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionExchangeProperty31FormDataService, AgreementSubmissionExchangeProperty31ServerSideFormDataService>();
            services.AddScoped<IManualFardRuralExchangeProperty31FormDataService, ManualFardRuralExchangeProperty31ServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanExchangeProperty31FormDataService, ManualFardUrbanExchangeProperty31ServerSideFormDataService>();
            services.AddScoped<IComputerizedFardExchangeProperty31ListingDataService, ComputerizedFardExchangeProperty31ServerSideListingDataService>();
            services.AddScoped<IManualFardExchangeProperty31ListingDataService, ManualFardExchangeProperty31ServerSideListingDataService>();
            services.AddScoped<IBreadCrumbListingDataService, BreadCrumbServerSideListingDataService>();
            services.AddScoped<IConveyance23FardDeleteFormDataService, Conveyance23FardDeleteServerSideFormDataService>();
            services.AddScoped<IPromissoryNote49A3BasicInfoFormDataService, PromissoryNote49A3BasicInfoServerSideFormDataService>();
            services.AddScoped<IPromissoryNote49A3BasicInfoListingDataService, PromissoryNote49A3BasicInfoServerSideListingDataService>();
            services.AddScoped<IPromissoryNote49BBasicInfoFormDataService, PromissoryNote49BBasicInfoServerSideFormDataService>();
            services.AddScoped<IPromissoryNote49BBasicInfoListingDataService, PromissoryNote49BBasicInfoServerSideListingDataService>();
            services.AddScoped<ILeaseTransfer63BasicInfoFormDataService, LeaseTransfer63BasicInfoServerSideFormDataService>();
            services.AddScoped<ILeaseTransfer63BasicInfoListingDataService, LeaseTransfer63BasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionLeaseTransfer63FormDataService, AgreementSubmissionLeaseTransfer63ServerSideFormDataService>();
            services.AddScoped<IManualFardRuralLeaseTransfer63FormDataService, ManualFardRuralLeaseTransfer63ServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanLeaseTransfer63FormDataService, ManualFardUrbanLeaseTransfer63ServerSideFormDataService>();
            services.AddScoped<IComputerizedFardLeaseTransfer63ListingDataService, ComputerizedFardLeaseTransfer63ServerSideListingDataService>();
            services.AddScoped<IManualFardLeaseTransfer63ListingDataService, ManualFardLeaseTransfer63ServerSideListingDataService>();
            services.AddScoped<IRelease55ABasicInfoFormDataService, Release55ABasicInfoServerSideFormDataService>();
            services.AddScoped<IRelease55ABasicInfoListingDataService, Release55ABasicInfoServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionRelease55AFormDataService, AgreementSubmissionRelease55AServerSideFormDataService>();
            services.AddScoped<IManualFardRuralRelease55AFormDataService, ManualFardRuralRelease55AServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanRelease55AFormDataService, ManualFardUrbanRelease55AServerSideFormDataService>();
            services.AddScoped<IComputerizedFardRelease55AListingDataService, ComputerizedFardRelease55AServerSideListingDataService>();
            services.AddScoped<IManualFardRelease55AListingDataService, ManualFardRelease55AServerSideListingDataService>();
            services.AddScoped<ICertificateOfSale18FormDataService, CertificateOfSale18ServerSideFormDataService>();
            services.AddScoped<ICertificateOfSale18ListingDataService, CertificateOfSale18ServerSideListingDataService>();
            services.AddScoped<IAgreementSubmissionCertificateOfSale18FormDataService, AgreementSubmissionCertificateOfSale18ServerSideFormDataService>();
            services.AddScoped<IManualFardRuralCertificateOfSale18FormDataService, ManualFardRuralCertificateOfSale18ServerSideFormDataService>();
            services.AddScoped<IManualFardUrbanCertificateOfSale18FormDataService, ManualFardUrbanCertificateOfSale18ServerSideFormDataService>();
            services.AddScoped<IComputerizedFardCertificateOfSale18ListingDataService, ComputerizedFardCertificateOfSale18ServerSideListingDataService>();
            services.AddScoped<IManualFardCertificateOfSale18ListingDataService, ManualFardCertificateOfSale18ServerSideListingDataService>();
            services.AddScoped<IDebentureOrParticipationTermBasicInfoFormDataService, DebentureOrParticipationTermBasicInfoServerSideFormDataService>();
            services.AddScoped<IDebentureOrParticipationTermBasicInfoListingDataService, DebentureOrParticipationTermBasicInfoServerSideListingDataService>();
            services.AddScoped<IRectification159BasicInfoFormDataService, Rectification159BasicInfoServerSideFormDataService>();
            services.AddScoped<IRectification159BasicInfoListingDataService, Rectification159BasicInfoServerSideListingDataService>();
            services.AddScoped<IDivorce29BasicInfoFormDataService, Divorce29BasicInfoServerSideFormDataService>();
            services.AddScoped<IDivorce29BasicInfoListingDataService, Divorce29BasicInfoServerSideListingDataService>();
            services.AddScoped<IMortgageforHousingFinanceivBasicInfoFormDataService, MortgageforHousingFinanceivBasicInfoServerSideFormDataService>();
            services.AddScoped<IMortgageforHousingFinanceivBasicInfoListingDataService, MortgageforHousingFinanceivBasicInfoServerSideListingDataService>();
            services.AddScoped<ISettlement58FormDataService, Settlement58ServerSideFormDataService>();
            services.AddScoped<ISettlement58ListingDataService, Settlement58ServerSideListingDataService>();
            services.AddScoped<IPropertyDetailFormDataService, PropertyDetailServerSideFormDataService>();
            services.AddScoped<IPropertyDetailListingDataService, PropertyDetailServerSideListingDataService>();
            services.AddScoped<ISurrenderOfLease61BFormDataService, SurrenderOfLease61BServerSideFormDataService>();
            services.AddScoped<ISurrenderOfLease61BListingDataService, SurrenderOfLease61BServerSideListingDataService>();
            services.AddScoped<IRespondentiaBond56BasicInfoFormDataService, RespondentiaBond56BasicInfoServerSideFormDataService>();
            services.AddScoped<IRespondentiaBond56BasicInfoListingDataService, RespondentiaBond56BasicInfoServerSideListingDataService>();
            services.AddScoped<IDeedAgreementBasicInfoFormDataService, DeedAgreementBasicInfoServerSideFormDataService>();
            services.AddScoped<IDeedAgreementBasicInfoListingDataService, DeedAgreementBasicInfoServerSideListingDataService>();
            services.AddScoped<ITransfer62bBasicInfoFormDataService, Transfer62bBasicInfoServerSideFormDataService>();
            services.AddScoped<ITransfer62bBasicInfoListingDataService, Transfer62bBasicInfoServerSideListingDataService>();
            services.AddScoped<IInboxListingDataService, InboxServerSideListingDataService>(); 
 services.AddScoped<IInboxStatsListingDataService, InboxStatsServerSideListingDataService>(); 
 services.AddScoped<IStepperFormVerificationFormDataService, StepperFormVerificationServerSideFormDataService>(); 
 //##NewService##
        }
    }
}
