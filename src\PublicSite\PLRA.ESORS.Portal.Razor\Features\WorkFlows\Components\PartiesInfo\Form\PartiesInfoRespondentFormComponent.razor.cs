using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Portal.ServiceContracts.SelectList;

namespace PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.PartiesInfo.Form
{
    public partial class PartiesInfoRespondentFormComponent
    {
        [Parameter]
        public long TaskId { get; set; }

        [Parameter]
        public int Step { get; set; }

        [Parameter]
        public PARTY_TYPE PARTY_TYPE { get; set; }

        [Parameter, EditorRequired]
        public bool DisplayLawyerField { get; set; } = true;

        [Parameter, EditorRequired]
        public string FormName { get; set; } = string.Empty;

        [Parameter, EditorRequired]
        public string? Heading_En { get; set; }

        [Parameter, EditorRequired]
        public string? Heading_Ur { get; set; }

        [Parameter]
        public bool FormHasListing { get; set; } = false;

        [SupplyParameterFromQuery(Name = "rowId")]
        public long RowId { get; set; } = 0;

        private string? _message;

        [SupplyParameterFromQuery(Name = "cnic")]
        public long CNIC { get; set; } = 0;

        [SupplyParameterFromQuery(Name = "partyType")]
        public string SELECTED_PARTY_TYPE { get; set; } = string.Empty;

        // Properties to track which fields are populated from API and should be disabled
        private readonly Dictionary<string, bool> _fieldsFromApi = new()
        {
            ["name"] = false,
            ["relation"] = false,
            ["relativename"] = false,
            ["address"] = false,
            ["phonenumber"] = false,
            ["email"] = false,
            ["cnic"] = false
        };

        // Simplified state management for CNIC and API data
        private readonly Dictionary<string, string?> _apiFieldValues = new();

        // Track the last CNIC that successfully returned API data
        private long? _lastApiCnic = null;

        // Simple flags for state tracking
        private bool _hasApiData = false;

        protected override async Task OnInitializedAsync()
        {
            Id = (long)PARTY_TYPE * 1_000_000_000_000 + (FormHasListing ? RowId : TaskId);
            var flagBit = 1L << 48;        // bit at position 48 only concat when need to fetch record with Id
            if (FormHasListing)
                Id |= flagBit;

            await base.OnInitializedAsync();

            if (SelectedItem != null)
            {
                SelectedItem.PARTY_TYPE = PARTY_TYPE;
                SelectedItem.Respondent_Id = SelectedItem.Respondent_Id ?? (FormHasListing ? RowId : 0);
                SelectedItem.EStampMainId = TaskId;

                // Handle CNIC search from query parameter (triggered by JavaScript redirect)
                if (SELECTED_PARTY_TYPE != null && (PARTY_TYPE)Enum.Parse(typeof(PARTY_TYPE), SELECTED_PARTY_TYPE) == PARTY_TYPE)
                {
                    if (CNIC > 0 && (!string.IsNullOrEmpty(SELECTED_PARTY_TYPE)))
                    {
                        await PerformCnicSearchFromQuery(CNIC);
                    }
                    else if (SelectedItem.Respondent_CNIC != null && SelectedItem.Respondent_CNIC > 0)
                    {
                        // When loading existing data, determine which fields might have come from API
                        await DetermineFieldSourcesFromExistingData();
                    }
                }
               
            }
        }

        public override async Task OnAfterSaveAsync(long key)
        {
            ArgumentNullException.ThrowIfNull(SelectedItem);

            string url = string.Empty;
            if (key > 0)
            {
                SelectedItem.Respondent_Id = key;
                _message = "Record has been saved successfully";
                url = NavigationManager?.AddQueryString(new { partyType = PARTY_TYPE })!;

            }
            if (FormHasListing)
            {

                SelectedItem = await CreateSelectedItem();
                url = NavigationManager?.AddQueryString(new { reloadparty = true, partyType = PARTY_TYPE })!;
            }

            NavigationManager?.NavigateTo(url);
        }

        /// <summary>
        /// Performs CNIC search triggered by query parameter from JavaScript redirect
        /// </summary>
        /// <param name="cnicToSearch">CNIC number to search</param>
        private async Task PerformCnicSearchFromQuery(long cnicToSearch)
        {
            try
            {
                if (cnicToSearch <= 0 || SelectedItem == null)
                {
                    ValidationError = "Please enter a valid CNIC number";
                    return;
                }

                using var scope = ScopeFactory?.CreateScope();
                var service = scope?.ServiceProvider.GetRequiredService<ISelectListDataService>();
                ArgumentNullException.ThrowIfNull(service);

                var person = await service.GetPersonDetail(cnicToSearch);
                if (person != null)
                {
                    await HandleApiDataFound(person, cnicToSearch);
                }
                else
                {
                    await HandleApiDataNotFound(cnicToSearch);
                }
            }
            catch (Exception ex)
            {
                await HandleApiError(cnicToSearch, ex);
            }
        }

        /// <summary>
        /// Compares API data with current form values and updates fields that differ
        /// </summary>
        /// <param name="person">Person data from API</param>
        /// <param name="cnicToSearch">CNIC that was searched</param>
        private async Task UpdateFieldsFromApiData(dynamic person, long cnicToSearch)
        {
            // Update CNIC if different
            if (!string.IsNullOrEmpty(person.CNIC) && Convert.ToInt64(person.CNIC) != SelectedItem.Respondent_CNIC)
            {
                SelectedItem.Respondent_CNIC = Convert.ToInt64(person.CNIC);
            }

            // Compare and update each field if API data differs from current value
            UpdateFieldIfDifferent("name", person.FirstName, SelectedItem.Respondent_Name,
                value => SelectedItem.Respondent_Name = value);

            UpdateFieldIfDifferent("relation", person.Relation, SelectedItem.Respondent_Relation,
                value => SelectedItem.Respondent_Relation = value);

            UpdateFieldIfDifferent("relativename", person.RelativeName, SelectedItem.Respondent_RelativeName,
                value => SelectedItem.Respondent_RelativeName = value);

            UpdateFieldIfDifferent("address", person.Address, SelectedItem.Respondent_Address,
                value => SelectedItem.Respondent_Address = value);

            UpdateFieldIfDifferent("email", person.Email, SelectedItem.Respondent_Email,
                value => SelectedItem.Respondent_Email = value);

            // Handle phone number with parsing
            if (!string.IsNullOrEmpty(person.Phone) && long.TryParse(person.Phone, out long phoneNumber))
            {
                var currentPhone = SelectedItem.Respondent_PhoneNumber?.ToString();
                UpdateFieldIfDifferent("phonenumber", person.Phone, currentPhone,
                    value => SelectedItem.Respondent_PhoneNumber = long.TryParse(value, out long phone) ? phone : null);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Determines which fields might have come from API based on existing data
        /// This is used when loading existing records to maintain field states
        /// </summary>
        private async Task DetermineFieldSourcesFromExistingData()
        {
            if (SelectedItem == null || SelectedItem.Respondent_CNIC == null || SelectedItem.Respondent_CNIC <= 0)
                return;

            try
            {
                // Check if the current data matches API data for this CNIC
                using var scope = ScopeFactory?.CreateScope();
                var service = scope?.ServiceProvider.GetRequiredService<ISelectListDataService>();
                ArgumentNullException.ThrowIfNull(service);

                var person = await service.GetPersonDetail(SelectedItem.Respondent_CNIC.Value);
                if (person != null)
                {
                    // Check each field to see if it matches API data
                    CheckFieldMatchesApi("name", person.FirstName, SelectedItem.Respondent_Name);
                    CheckFieldMatchesApi("relation", person.Relation, SelectedItem.Respondent_Relation);
                    CheckFieldMatchesApi("relativename", person.RelativeName, SelectedItem.Respondent_RelativeName);
                    CheckFieldMatchesApi("address", person.Address, SelectedItem.Respondent_Address);
                    CheckFieldMatchesApi("email", person.Email, SelectedItem.Respondent_Email);

                    // Handle phone number separately due to type conversion
                    if (!string.IsNullOrEmpty(person.Phone) &&
                        long.TryParse(person.Phone, out var phoneNumber) &&
                        SelectedItem.Respondent_PhoneNumber == phoneNumber)
                    {
                        _fieldsFromApi["phonenumber"] = true;
                        _apiFieldValues["phonenumber"] = person.Phone;
                    }

                    // If any fields match API data, set API state
                    if (_fieldsFromApi.Values.Any(x => x))
                    {
                        _hasApiData = true;
                        _lastApiCnic = SelectedItem.Respondent_CNIC;
                    }
                }
            }
            catch (Exception)
            {
                // If API call fails, treat all fields as manually entered
                // This ensures fields remain editable
            }
        }

        /// <summary>
        /// Checks if a field should be disabled based on whether it was populated from API
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <returns>True if field should be disabled</returns>
        public bool IsFieldDisabled(string fieldName)
        {
            var key = fieldName.ToLower();
            return _fieldsFromApi.ContainsKey(key) && _fieldsFromApi[key];
        }

        /// <summary>
        /// Gets CSS class for disabled fields
        /// </summary>
        /// <param name="fieldName">Name of the field</param>
        /// <returns>CSS class string</returns>
        public string GetFieldCssClass(string fieldName)
        {
            var baseClass = "form-control";
            if (fieldName == "name" || fieldName == "relation" || fieldName == "relativename" || fieldName == "address")
            {
                baseClass += " urdu-text";
            }

            if (IsFieldDisabled(fieldName))
            {
                baseClass += " bg-light text-muted";
            }

            return baseClass;
        }

        /// <summary>
        /// Checks if a field value has been modified from its original API value
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <param name="currentValue">Current field value</param>
        /// <returns>True if field has been modified from API value</returns>
        public bool IsFieldModifiedFromApi(string fieldName, string? currentValue)
        {
            var key = fieldName.ToLower();
            if (!_fieldsFromApi.ContainsKey(key) || !_fieldsFromApi[key])
                return false;

            if (!_apiFieldValues.ContainsKey(key))
                return false;

            return !string.Equals(currentValue, _apiFieldValues[key], StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets a message indicating the source of field data
        /// </summary>
        /// <param name="fieldName">Name of the field</param>
        /// <returns>Message string or null</returns>
        public string? GetFieldSourceMessage(string fieldName)
        {
            if (IsFieldDisabled(fieldName))
            {
                return "This field was populated from API and is disabled";
            }
            return null;
        }

        /// <summary>
        /// Handles successful API data retrieval
        /// </summary>
        private async Task HandleApiDataFound(dynamic person, long cnicToSearch)
        {
            // If this is a different CNIC than the last successful one, clear previous API fields
            if (_lastApiCnic.HasValue && _lastApiCnic.Value != cnicToSearch && _hasApiData)
            {
                ClearApiFields();
            }

            // Clear any previous validation errors
            ValidationError = null;
            _message = "Record found and data populated from API";

            // Update fields with API data
            await UpdateFieldsFromApiData(person, cnicToSearch);

            // Update state
            _hasApiData = true;
            _lastApiCnic = cnicToSearch;
        }

        /// <summary>
        /// Handles case when API search returns no data
        /// </summary>
        private async Task HandleApiDataNotFound(long cnicToSearch)
        {
            // If this is a different CNIC and we had previous API data, clear it
            if (_lastApiCnic.HasValue && _lastApiCnic.Value != cnicToSearch && _hasApiData)
            {
                ClearApiFields();
            }

            ValidationError = $"No record found for CNIC: {cnicToSearch}";
            _message = null;

            await Task.CompletedTask;
        }

        /// <summary>
        /// Handles API errors (network issues, exceptions)
        /// </summary>
        private async Task HandleApiError(long cnicToSearch, Exception ex)
        {
            ValidationError = $"Error searching CNIC: {ex.Message}";
            _message = null;

            // Don't clear existing data on API errors - preserve user's work
            await Task.CompletedTask;
        }

        /// <summary>
        /// Clears only the fields that were populated from API
        /// </summary>
        private void ClearApiFields()
        {
            if (SelectedItem == null) return;

            foreach (var field in _fieldsFromApi.Where(f => f.Value).ToList())
            {
                switch (field.Key)
                {
                    case "name":
                        SelectedItem.Respondent_Name = null;
                        break;
                    case "relation":
                        SelectedItem.Respondent_Relation = null;
                        break;
                    case "relativename":
                        SelectedItem.Respondent_RelativeName = null;
                        break;
                    case "address":
                        SelectedItem.Respondent_Address = null;
                        break;
                    case "phonenumber":
                        SelectedItem.Respondent_PhoneNumber = null;
                        break;
                    case "email":
                        SelectedItem.Respondent_Email = null;
                        break;
                }

                // Reset field tracking
                _fieldsFromApi[field.Key] = false;
            }

            // Clear API values and reset state
            _apiFieldValues.Clear();
            _hasApiData = false;
            _lastApiCnic = null;
        }

        /// <summary>
        /// Updates a field if API data differs from current value
        /// </summary>
        private void UpdateFieldIfDifferent(string fieldKey, string? apiValue, string? currentValue, Action<string> updateAction)
        {
            if (string.IsNullOrEmpty(apiValue)) return;

            if (!string.Equals(apiValue, currentValue, StringComparison.OrdinalIgnoreCase))
            {
                updateAction(apiValue);
                _fieldsFromApi[fieldKey] = true;
                _apiFieldValues[fieldKey] = apiValue;
            }
        }

        /// <summary>
        /// Checks if a field matches API data (used for existing data detection)
        /// </summary>
        private void CheckFieldMatchesApi(string fieldKey, string? apiValue, string? currentValue)
        {
            if (!string.IsNullOrEmpty(apiValue) &&
                string.Equals(apiValue, currentValue, StringComparison.OrdinalIgnoreCase))
            {
                _fieldsFromApi[fieldKey] = true;
                _apiFieldValues[fieldKey] = apiValue;
            }
        }
    }
}