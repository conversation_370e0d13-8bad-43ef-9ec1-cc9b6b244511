﻿using Microsoft.Extensions.Options;
using PLRA.ESORS.ServiceContracts;
using PLRA.ESORS.ServiceContracts.Models;
using System.Text.Json;

namespace PLRA.ESORS.Server.DataServices.Services.TerritoryService
{
    public class StaticTerritoryService : IStaticTerritoryService
    {
        private readonly HttpClient _httpClient;
        public readonly TerritorySettings _territorySettings;
        private readonly IErrorLoggingService _errorLoggingService;

        public StaticTerritoryService(HttpClient httpClient, IOptions<TerritorySettings> territorySettings, IErrorLoggingService errorLoggingService)
        {
            _httpClient = httpClient;
            _territorySettings = territorySettings.Value;
            _errorLoggingService = errorLoggingService;
        }

        public async Task<DistrictTehsilResponse?> GetDistrictAndTehsils()
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _territorySettings.Authorization);
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("access-key", _territorySettings.AccessKey);

                var response = await _httpClient.GetAsync($"{_territorySettings.URL}/DistrictAndTehsil");

                if (response.IsSuccessStatusCode)
                {
                    var territory = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        ReadCommentHandling = JsonCommentHandling.Skip
                    };
                    var territoryResponse = JsonSerializer.Deserialize<DistrictTehsilResponse>(territory, options);
                    return territoryResponse;
                }

                return null;
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogExternalServiceErrorAsync(
                    nameof(StaticTerritoryService),
                    nameof(GetDistrictAndTehsils),
                    ex,
                    "Territory Service",
                    $"{_territorySettings.URL}/DistrictAndTehsil"
                );
                throw;
            }
        }

        public async Task<MauzaResponse?> GetMauzaByTehsilId(int tehsilId)
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _territorySettings.Authorization);
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("access-key", _territorySettings.AccessKey);

            var response = await _httpClient.GetAsync($"{_territorySettings.URL}/Mauza?tehsilId={tehsilId}");

            if (response.IsSuccessStatusCode)
            {
                var territory = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    ReadCommentHandling = JsonCommentHandling.Skip
                };
                var territoryResponse = JsonSerializer.Deserialize<MauzaResponse>(territory, options);
                return territoryResponse;
            }

            return null;
        }

    }
}
