﻿using PLRA.ESORS.Portal.ServiceContracts.Models;
using System.Text.Json.Serialization;
using System.Text.Json;

namespace PLRA.ESORS.Portal.Server.DataServices.Services
{
    public class PaymentsRuleEngineService
    {
        
        private PaymentRuleConfig RuleEngine { get; set; }
        public PaymentsRuleEngineService()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "wwwroot", "PaymentRules.json");
            var json = File.ReadAllText(filePath);
            var ruleEngine = FromJson(json);
            RuleEngine = ruleEngine;
        }

        public static PaymentRuleConfig FromJson(string json)
        {
            return JsonSerializer.Deserialize<PaymentRuleConfig>(json)!;
        }

        public string ToJson()
        {
            return JsonSerializer.Serialize(this);
        }

        public PaymentRecord Calculate(int workflowId, decimal amount, decimal propertyValue = 0)
        {
            var deed = RuleEngine.Deeds.FirstOrDefault(x=>x.WorkFlowId == workflowId)!;
            if (deed == null) throw new Exception("No Rule Engine found");

            decimal stampDuty = 0, registrationFee = 0, cvt = 0, fbr236KFees = 0, fbr236CFees = 0, ttipFees = 0;

            if (deed == null) throw new ArgumentNullException(nameof(deed));

            if (deed.HasStampDuty)
            {
                var duty = deed.Fees.StampDuty;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        stampDuty = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        stampDuty = ApplyMinMax(percentageAmount, duty.MinAmount, percentageAmount);
                        break;
                    case "UserDefined":
                        stampDuty = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        stampDuty = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.HasRegistrationFee)
            {
                var duty = deed.Fees.RegistrationFee;

                switch (duty.CalculationType)
                {
                    case "Fixed":
                        registrationFee = duty.FixedAmount ?? 0;
                        break;
                    case "Percent":
                        var percentageAmount = amount * (duty.Percentage ?? 0) / 100;
                        registrationFee = ApplyMinMax(percentageAmount, duty.MinAmount, duty.MaxAmount);
                        break;
                    case "UserDefined":
                        registrationFee = CalculateUserDefined(deed, amount, propertyValue);
                        break;
                    case "Rule":
                        registrationFee = CalculateByRule(deed, duty, amount, propertyValue);
                        break;
                }
            }
            if (deed.hasCVT)
            {

            }
            if (deed.HasFBRFees)
            {

            }
            if (deed.HasTTIPFees)
            {

            }

            return new PaymentRecord(deed, stampDuty, registrationFee, cvt, fbr236KFees, fbr236CFees, ttipFees);
        }

        private decimal ApplyMinMax(decimal amount, decimal? min, decimal? max)
        {
            if (min.HasValue && amount < min.Value) return min.Value;
            if (max.HasValue && amount > max.Value) return max.Value;
            return amount;
        }

        private decimal CalculateUserDefined(Deed deed, decimal amount, decimal propertyValue)
        {
            // Court fee specific calculation 
            if (amount > deed.Fees.StampDuty.MinAmount && amount <= deed.Fees.StampDuty.MaxAmount)
            {
                return ApplyMinMax(amount, deed.Fees.StampDuty.MinAmount, deed.Fees.StampDuty.MaxAmount);
            }
            throw new ArgumentException($"{deed.Fees.StampDuty.CalculationRule}");
        }

        private decimal CalculateByRule(Deed deed, FeeCalculation feeCalculation, decimal amount, decimal propertyValue)
        {
            // Handle registration fee rules
            if (feeCalculation.AccountHeadName?.Contains("Registration Fee") == true)
            {
                return CalculateRegistrationFeeByRule(deed, feeCalculation, amount);
            }

            // Handle stamp duty rules
            if (feeCalculation.AccountHeadName?.Contains("Stamp") == true)
            {
                return CalculateStampDutyByRule(deed, feeCalculation, amount, propertyValue);
            }

            // Default fallback
            return feeCalculation.MinAmount ?? 0;
        }

        private decimal CalculateRegistrationFeeByRule(Deed deed, FeeCalculation feeCalculation, decimal amount)
        {
            // Handle the most common registration fee rule pattern
            // "Rs.500/-, if the amount of consideration does not exceed Rs.500,000/-; and Rs.1000/-, if amount of consideration exceeds Rs.500,000/-."
            if (feeCalculation.CalculationRule?.Contains("Rs.500/-") == true &&
                feeCalculation.CalculationRule?.Contains("Rs.1000/-") == true &&
                feeCalculation.CalculationRule?.Contains("Rs.500,000/-") == true)
            {
                return amount <= 500000 ? 500 : 1000;
            }

            // Handle specific rules for workflow 63 (LEASE - 35(1(a))) and similar
            if (deed.WorkFlowId == 63 && deed.DeedName.Contains("LEASE - 35(1(a))"))
            {
                // Rs.500/-, if the amount of consideration does not exceed Rs.500,000/-; and Rs.1000/-, if amount of consideration exceeds Rs.500,000/-.
                return amount <= 500000 ? 500 : 1000;
            }

            // Handle other specific registration fee rules
            if (feeCalculation.CalculationRule?.Contains("additional stamp duty shall be charged equal to the amount of the registration fee paid on the principal mortgage deed") == true)
            {
                // This would require additional logic to get the principal mortgage deed fee
                // For now, return a default amount
                return 500; // Default registration fee
            }

            // Default registration fee
            return 500;
        }

        private decimal CalculateStampDutyByRule(Deed deed, FeeCalculation feeCalculation, decimal amount, decimal propertyValue)
        {
            // Handle existing stamp duty rules
            if (deed.DeedName.Contains("FURTHER CHARGE"))
            {
                return amount * 0.02m; // 2% for further charge
            }

            if (deed.DeedName.Contains("MORTGAGE"))
            {
                if (deed.DeedName.Contains("HOUSING FINANCE"))
                {
                    // Housing finance mortgage rules
                    if (amount <= 100000) return 250;
                    if (amount <= 500000) return 500;
                    if (amount <= 1000000) return 1000;
                    return 1500;
                }
                return amount * 0.03m; // Default 3% for mortgage
            }

            // Handle urban/rural property rules
            if (feeCalculation.CalculationRule?.Contains("urban area") == true &&
                feeCalculation.CalculationRule?.Contains("Rural area") == true)
            {
                // This would require property location information
                // For now, assume urban area (1%) vs rural area (3%)
                // This logic would need to be enhanced with actual property location data
                return amount * 0.01m; // Default to urban area rate
            }

            // Handle partnership rules
            if (deed.DeedName.Contains("PARTNERSHIP") &&
                feeCalculation.CalculationRule?.Contains("capital of the partnership") == true)
            {
                return amount <= 10000 ? 200 : 1000;
            }

            // Handle other specific rules based on calculation rule text
            if (feeCalculation.CalculationRule?.Contains("10 rupees for every 1000") == true)
            {
                return Math.Max(10, (amount / 1000) * 10);
            }

            // Default to min amount if no specific rule matches
            return feeCalculation.MinAmount ?? 0;
        }

    }

}
