using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PLRA.ESORS.Portal.ServiceContracts;
using PLRA.ESORS.Portal.ServiceContracts.Models;
using System.Data;
using System.Xml;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.Server.Data.Entities;
using PLRA.ESORS.Framework.Enums;
using Microsoft.EntityFrameworkCore;
using NadraVerificationService;
using System.ServiceModel;
using PLRA.ESORS.Framework;
using System.Text;

namespace PLRA.ESORS.Portal.Server.DataServices.Services.NadraService
{
    /// <summary>
    /// LRMIS-style NADRA Biometric Service using WSDL Connected Service Reference
    /// Follows LRMIS patterns for biometric verification workflow with database integration
    /// </summary>
    public interface INadraBiometricApiService
    {
        Task<NadraBiometricResponse> VerifyBiometricAsync(NadraBiometricRequest request);
        Task<bool> TestConnectionAsync();
        Task<NadraBiometricResponse> GetVerificationStatusAsync(long partyId, long taskId);
        Task<LrmisVerificationResult> VerifyFingerPrintLrmisStyleAsync(long cnic, string fingerTemplate, long partyId, long taskId);
    }

    public class NadraBiometricApiService : INadraBiometricApiService
    {
        private readonly ILogger<NadraBiometricApiService> _logger;
        private readonly NadraSettings _nadraSettings;
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticatedUser _authenticatedUser;
        private readonly INadraApiService _nadraApiService;
        private readonly IErrorLoggingService _errorLoggingService;

        // LRMIS-style constants
        private const string DEFAULT_FRANCHISE_ID = "3152";
        private const string DEFAULT_AREA_NAME = "punjab";
        private const byte DEFAULT_FINGER_INDEX = 10; // Thumb
        private const string VERIFICATION_SUCCESS_CODE = "100";

        public NadraBiometricApiService(
            ILogger<NadraBiometricApiService> logger,
            IOptions<NadraSettings> nadraSettings,
            ApplicationDbContext context,
            IAuthenticatedUser authenticatedUser,
            INadraApiService nadraApiService,
            IErrorLoggingService errorLoggingService)
        {
            _logger = logger;
            _nadraSettings = nadraSettings.Value;
            _context = context;
            _authenticatedUser = authenticatedUser;
            _nadraApiService = nadraApiService;
            _errorLoggingService = errorLoggingService;
        }

        /// <summary>
        /// LRMIS-style NADRA fingerprint verification with comprehensive workflow
        /// </summary>
        public async Task<LrmisVerificationResult> VerifyFingerPrintLrmisStyleAsync(long cnic, string fingerTemplate, long partyId, long taskId)
        {
            try
            {
                _logger.LogInformation("Starting LRMIS-style NADRA verification for CNIC: {CNIC}, Party: {PartyId}, Task: {TaskId}",
                    cnic, partyId, taskId);

                // Validate party exists (LRMIS pattern)
                var party = await _context.Parties.FirstOrDefaultAsync(x =>
                    x.Id == partyId &&
                    x.EStampMainId == taskId &&
                    x.CNIC == cnic);

                if (party == null)
                {
                    _logger.LogWarning("Party validation failed for CNIC: {CNIC}, PartyId: {PartyId}, TaskId: {TaskId}",
                        cnic, partyId, taskId);
                    return CreateLrmisErrorResult("PARTY_NOT_FOUND", "Party not found in database");
                }

                // Call NADRA verification service using LRMIS pattern
                var dataSet = await _nadraApiService.VerifyFingerPrintAsync(cnic, fingerTemplate, _nadraSettings.TemplateType);

                if (dataSet == null || dataSet.Tables.Count == 0)
                {
                    _logger.LogWarning("Empty or null dataset received from NADRA for CNIC: {CNIC}", cnic);
                    return CreateLrmisErrorResult("EMPTY_RESPONSE", "No response received from NADRA service");
                }

                // Parse LRMIS-style response
                var lrmisResult = ParseLrmisDataSetResponse(dataSet, cnic);

                // Save to database following LRMIS pattern
                var biometricVerificationId = await SaveLrmisVerificationAsync(partyId, taskId, dataSet, lrmisResult);
                lrmisResult.BiometricVerificationId = biometricVerificationId;

                _logger.LogInformation("LRMIS-style NADRA verification completed for CNIC: {CNIC}. Code: {Code}, Verified: {IsVerified}",
                    cnic, lrmisResult.StatusCode, lrmisResult.IsVerified);

                return lrmisResult;
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogExternalServiceErrorAsync(
                    nameof(NadraBiometricApiService),
                    nameof(VerifyFingerPrintLrmisStyleAsync),
                    ex,
                    "NADRA Biometric Service",
                    _nadraSettings.URL
                );

                _logger.LogError(ex, "Error during LRMIS-style NADRA verification for CNIC: {CNIC}", cnic);
                return CreateLrmisErrorResult("SERVICE_ERROR", $"Verification failed: {ex.Message}");
            }
        }

        public async Task<NadraBiometricResponse> VerifyBiometricAsync(NadraBiometricRequest request)
        {
            try
            {
                _logger.LogInformation("Starting enhanced NADRA biometric verification for CNIC: {CNIC}, Party: {PartyId}, Task: {TaskId}",
                    request.CNIC, request.PartyId, request.TaskId);

                // Use LRMIS-style verification internally
                var lrmisResult = await VerifyFingerPrintLrmisStyleAsync(request.CNIC, request.FingerTemplate, request.PartyId, request.TaskId);

                // Convert LRMIS result to enhanced response
                var response = new NadraBiometricResponse
                {
                    Success = lrmisResult.IsVerified,
                    StatusCode = lrmisResult.StatusCode,
                    Message = lrmisResult.Message,
                    SessionId = lrmisResult.SessionId,
                    TransactionId = lrmisResult.TransactionId,
                    IsVerified = lrmisResult.IsVerified,
                    CitizenName = lrmisResult.CitizenName,
                    FatherName = lrmisResult.FatherName,
                    DateOfBirth = lrmisResult.DateOfBirth,
                    Gender = lrmisResult.Gender,
                    Address = lrmisResult.Address,
                    VerificationDateTime = DateTime.UtcNow,
                    BiometricVerificationId = lrmisResult.BiometricVerificationId,
                    RawResponse = lrmisResult.RawDataSet?.GetXml() ?? "",
                    NadraBiometricData = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        StatusCode = lrmisResult.StatusCode,
                        Message = lrmisResult.Message,
                        IsVerified = lrmisResult.IsVerified,
                        CitizenName = lrmisResult.CitizenName,
                        FatherName = lrmisResult.FatherName,
                        DateOfBirth = lrmisResult.DateOfBirth,
                        Gender = lrmisResult.Gender,
                        Address = lrmisResult.Address,
                        VerificationDateTime = DateTime.UtcNow,
                        BiometricVerificationId = lrmisResult.BiometricVerificationId
                    })
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during enhanced NADRA verification for CNIC: {CNIC}", request.CNIC);
                return CreateErrorResponse($"Verification failed: {ex.Message}");
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            BioVeriSysGenericClient? client = null;
            try
            {
                _logger.LogInformation("Testing NADRA service connection");

                client = CreateNadraClient();
                
                // Try to open the connection
                await client.OpenAsync();

                _logger.LogInformation("Successfully connected to NADRA service");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "NADRA service connection test failed");
                return false;
            }
            finally
            {
                await SafeCloseClient(client);
            }
        }

        public async Task<NadraBiometricResponse> GetVerificationStatusAsync(long partyId, long taskId)
        {
            try
            {
                var verification = await _context.BiometricVerifications
                    .FirstOrDefaultAsync(x => 
                        x.PartyId == partyId && 
                        x.EStampMainId == taskId && 
                        x.VERIFICATION_TYPE == VERIFICATION_TYPE.Biometric);

                if (verification == null)
                {
                    return CreateErrorResponse("No verification record found");
                }

                // Try to parse stored response
                if (!string.IsNullOrEmpty(verification.Data))
                {
                    try
                    {
                        var storedData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(verification.Data);
                        
                        return new NadraBiometricResponse
                        {
                            Success = storedData.TryGetProperty("IsVerified", out var isVerified) && isVerified.GetBoolean(),
                            StatusCode = storedData.TryGetProperty("StatusCode", out var statusCode) ? statusCode.GetString() ?? "" : "",
                            Message = storedData.TryGetProperty("Message", out var message) ? message.GetString() ?? "" : "",
                            IsVerified = storedData.TryGetProperty("IsVerified", out var verified) && verified.GetBoolean(),
                            CitizenName = storedData.TryGetProperty("CitizenName", out var citizenName) ? citizenName.GetString() ?? "" : "",
                            BiometricVerificationId = verification.Id,
                            VerificationDateTime = verification.CreatedAt ?? DateTime.UtcNow,
                            NadraBiometricData = verification.Data
                        };
                    }
                    catch (System.Text.Json.JsonException)
                    {
                        // If JSON parsing fails, assume it's XML and parse accordingly
                        var parsedResponse = ParseNadraResponse(verification.Data);
                        return new NadraBiometricResponse
                        {
                            Success = parsedResponse.IsVerified,
                            StatusCode = parsedResponse.StatusCode,
                            Message = parsedResponse.Message,
                            IsVerified = parsedResponse.IsVerified,
                            CitizenName = parsedResponse.CitizenName,
                            BiometricVerificationId = verification.Id,
                            VerificationDateTime = verification.CreatedAt ?? DateTime.UtcNow
                        };
                    }
                }

                return CreateErrorResponse("Invalid verification data");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification status for PartyId: {PartyId}, TaskId: {TaskId}", partyId, taskId);
                return CreateErrorResponse($"Error retrieving verification status: {ex.Message}");
            }
        }

        /// <summary>
        /// Parse LRMIS-style DataSet response from NADRA
        /// </summary>
        private LrmisVerificationResult ParseLrmisDataSetResponse(DataSet dataSet, long cnic)
        {
            var result = new LrmisVerificationResult
            {
                CNIC = cnic,
                VerificationDateTime = DateTime.UtcNow,
                RawDataSet = dataSet
            };

            try
            {
                // Parse RESPONSE_STATUS table (LRMIS pattern)
                if (dataSet.Tables.Contains("RESPONSE_STATUS"))
                {
                    var statusTable = dataSet.Tables["RESPONSE_STATUS"];
                    if (statusTable.Rows.Count > 0)
                    {
                        result.StatusCode = statusTable.Rows[0]["CODE"]?.ToString() ?? "";
                        result.Message = statusTable.Rows[0]["MESSAGE"]?.ToString() ?? "";
                        result.IsVerified = result.StatusCode == VERIFICATION_SUCCESS_CODE;

                        _logger.LogDebug("LRMIS response status for CNIC {CNIC}: {Code} - {Message}",
                            cnic, result.StatusCode, result.Message);
                    }
                }

                // Parse CITIZEN_DATA table (LRMIS pattern)
                if (dataSet.Tables.Contains("CITIZEN_DATA"))
                {
                    var citizenTable = dataSet.Tables["CITIZEN_DATA"];
                    if (citizenTable.Rows.Count > 0)
                    {
                        result.CitizenName = citizenTable.Rows[0]["NAME"]?.ToString() ?? "";
                        result.FatherName = citizenTable.Rows[0]["FATHER_NAME"]?.ToString() ?? "";
                        result.DateOfBirth = citizenTable.Rows[0]["DATE_OF_BIRTH"]?.ToString() ?? "";
                        result.Gender = citizenTable.Rows[0]["GENDER"]?.ToString() ?? "";
                        result.Address = citizenTable.Rows[0]["ADDRESS"]?.ToString() ?? "";

                        _logger.LogDebug("LRMIS citizen data parsed for CNIC {CNIC}: {Name}",
                            cnic, result.CitizenName);
                    }
                }

                // Parse SESSION_INFO table (LRMIS pattern)
                if (dataSet.Tables.Contains("SESSION_INFO"))
                {
                    var sessionTable = dataSet.Tables["SESSION_INFO"];
                    if (sessionTable.Rows.Count > 0)
                    {
                        result.SessionId = sessionTable.Rows[0]["SESSION_ID"]?.ToString() ?? "";
                        result.TransactionId = sessionTable.Rows[0]["TRANSACTION_ID"]?.ToString() ?? "";
                    }
                }

                // Parse FINGER table (LRMIS specific)
                if (dataSet.Tables.Contains("FINGER"))
                {
                    var fingerTable = dataSet.Tables["FINGER"];
                    result.FingerCount = fingerTable.Rows.Count;
                    _logger.LogDebug("LRMIS finger data contains {FingerCount} records", result.FingerCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing LRMIS DataSet response for CNIC: {CNIC}", cnic);
                result.StatusCode = "PARSE_ERROR";
                result.Message = $"Error parsing response: {ex.Message}";
                result.IsVerified = false;
            }

            return result;
        }

        /// <summary>
        /// Save LRMIS-style verification to database
        /// </summary>
        private async Task<long> SaveLrmisVerificationAsync(long partyId, long taskId, DataSet dataSet, LrmisVerificationResult result)
        {
            try
            {
                // Check if verification already exists
                var existingVerification = await _context.BiometricVerifications
                    .FirstOrDefaultAsync(x =>
                        x.PartyId == partyId &&
                        x.EStampMainId == taskId &&
                        x.VERIFICATION_TYPE == VERIFICATION_TYPE.Biometric);

                // Create LRMIS-style JSON data
                var lrmisData = System.Text.Json.JsonSerializer.Serialize(new
                {
                    StatusCode = result.StatusCode,
                    Message = result.Message,
                    IsVerified = result.IsVerified,
                    CitizenName = result.CitizenName,
                    FatherName = result.FatherName,
                    DateOfBirth = result.DateOfBirth,
                    Gender = result.Gender,
                    Address = result.Address,
                    SessionId = result.SessionId,
                    TransactionId = result.TransactionId,
                    FingerCount = result.FingerCount,
                    VerificationDateTime = result.VerificationDateTime,
                    RawDataSetXml = dataSet.GetXml(),
                    LrmisStyle = true
                });

                if (existingVerification != null)
                {
                    // Update existing record
                    existingVerification.Data = lrmisData;
                    existingVerification.UpdatedAt = DateTime.UtcNow;
                    existingVerification.UpdatedBy = _authenticatedUser.UserId;

                    await _context.SaveChangesAsync();
                    return existingVerification.Id;
                }
                else
                {
                    // Create new record
                    var verification = new BiometricVerification
                    {
                        Id = _context.GetNextValueOfSequence(ApplicationDbContext.BiometricSequence),
                        PartyId = partyId,
                        EStampMainId = taskId,
                        VERIFICATION_TYPE = VERIFICATION_TYPE.Biometric,
                        Data = lrmisData,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _authenticatedUser.UserId
                    };

                    _context.BiometricVerifications.Add(verification);
                    await _context.SaveChangesAsync();

                    return verification.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving LRMIS verification to database");
                throw;
            }
        }

        /// <summary>
        /// Create LRMIS-style error result
        /// </summary>
        private LrmisVerificationResult CreateLrmisErrorResult(string errorCode, string errorMessage)
        {
            return new LrmisVerificationResult
            {
                StatusCode = errorCode,
                Message = errorMessage,
                IsVerified = false,
                VerificationDateTime = DateTime.UtcNow
            };
        }

        private void ValidateRequest(NadraBiometricRequest request)
        {
            if (request.CNIC <= 0)
                throw new ArgumentException("Invalid CNIC", nameof(request.CNIC));

            if (request.PartyId <= 0)
                throw new ArgumentException("Invalid PartyId", nameof(request.PartyId));

            if (request.TaskId <= 0)
                throw new ArgumentException("Invalid TaskId", nameof(request.TaskId));

            if (string.IsNullOrWhiteSpace(request.FingerTemplate))
                throw new ArgumentException("FingerTemplate cannot be empty", nameof(request.FingerTemplate));
        }

        private BioVeriSysGenericClient CreateNadraClient()
        {
            var binding = new BasicHttpBinding(BasicHttpSecurityMode.Transport)
            {
                MaxBufferSize = int.MaxValue,
                MaxReceivedMessageSize = int.MaxValue,
                SendTimeout = TimeSpan.FromMinutes(5),
                ReceiveTimeout = TimeSpan.FromMinutes(5),
                OpenTimeout = TimeSpan.FromMinutes(2),
                CloseTimeout = TimeSpan.FromMinutes(2)
            };

            var endpoint = new EndpointAddress(_nadraSettings.URL);
            var client = new BioVeriSysGenericClient(binding, endpoint);

            _logger.LogDebug("Created NADRA WCF client with endpoint: {Endpoint}", _nadraSettings.URL);
            return client;
        }

        private async Task<string> CallNadraServiceAsync(BioVeriSysGenericClient client, NadraBiometricRequest request)
        {
            var sessionId = Guid.NewGuid().ToString();
            var requestXml = BuildRequestXml(request.CNIC, sessionId, 10, request.FingerTemplate, "punjab");
            
            _logger.LogDebug("Calling NADRA service with XML request for CNIC: {CNIC}", request.CNIC);

            var franchiseId = _nadraSettings.Username;
            var response = await client.VerifyFingerPrintsAsync(franchiseId, requestXml);

            if (string.IsNullOrEmpty(response))
            {
                throw new InvalidOperationException("Empty response received from NADRA service");
            }

            return response;
        }

        private string BuildRequestXml(long cnic, string sessionId, byte fingerIndex, string fingerTemplate, string areaName)
        {
            // Generate transaction ID following BioVeriSys sample pattern
            Random random = new Random();
            string transactionId = "1009" + random.Next(1000000, 9999999).ToString("0000000") + random.Next(10000000, 99999999).ToString("00000000");

            // Convert fingerprint template to Base64 if not already encoded
            string base64FingerTemplate = fingerTemplate;
            try
            {
                // Check if it's already Base64 encoded
                Convert.FromBase64String(fingerTemplate);
            }
            catch
            {
                // If not Base64, convert the bytes to Base64
                byte[] templateBytes = System.Text.Encoding.UTF8.GetBytes(fingerTemplate);
                base64FingerTemplate = Convert.ToBase64String(templateBytes);
            }

            // Build XML request exactly matching BioVeriSys sample code structure
            var xmlRequest = $@"<BIOMETRIC_VERIFICATION><USER_VERIFICATION><USERNAME>{SecurityHelper.EscapeXml(_nadraSettings.Username)}</USERNAME><PASSWORD>{SecurityHelper.EscapeXml(_nadraSettings.Password)}</PASSWORD></USER_VERIFICATION> " +
                           " <REQUEST_DATA> " +
                               " <TRANSACTION_ID>{transactionId}</TRANSACTION_ID> " +
                               " <SESSION_ID></SESSION_ID> " +
                               " <CITIZEN_NUMBER>{cnic}</CITIZEN_NUMBER> " +
                               " <CONTACT_NUMBER></CONTACT_NUMBER> " +
                               " <FINGER_INDEX>{fingerIndex}</FINGER_INDEX> " +
                               " <FINGER_TEMPLATE>{base64FingerTemplate}</FINGER_TEMPLATE> " +
                               " <TEMPLATE_TYPE>SAGEM_PKMAT</TEMPLATE_TYPE> " +
                               " <AREA_NAME>{areaName.ToUpper()}</AREA_NAME> " +
                               " </REQUEST_DATA> " +
                           " </BIOMETRIC_VERIFICATION>";

            return xmlRequest;
        }

        private NadraParsedResponse ParseNadraResponse(string xmlResponse)
        {
            try
            {
                var dataSet = new DataSet();
                using (var reader = new XmlTextReader(new StringReader(xmlResponse)))
                {
                    dataSet.ReadXml(reader);
                }

                var response = new NadraParsedResponse();

                // Parse response status
                if (dataSet.Tables.Contains("RESPONSE_STATUS"))
                {
                    var statusTable = dataSet.Tables["RESPONSE_STATUS"];
                    if (statusTable.Rows.Count > 0)
                    {
                        response.StatusCode = statusTable.Rows[0]["CODE"]?.ToString() ?? "";
                        response.Message = statusTable.Rows[0]["MESSAGE"]?.ToString() ?? "";
                    }
                }

                // Parse citizen data
                if (dataSet.Tables.Contains("CITIZEN_DATA"))
                {
                    var citizenTable = dataSet.Tables["CITIZEN_DATA"];
                    if (citizenTable.Rows.Count > 0)
                    {
                        response.CitizenName = citizenTable.Rows[0]["NAME"]?.ToString() ?? "";
                        response.FatherName = citizenTable.Rows[0]["FATHER_NAME"]?.ToString() ?? "";
                        response.DateOfBirth = citizenTable.Rows[0]["DATE_OF_BIRTH"]?.ToString() ?? "";
                        response.Gender = citizenTable.Rows[0]["GENDER"]?.ToString() ?? "";
                        response.Address = citizenTable.Rows[0]["ADDRESS"]?.ToString() ?? "";
                    }
                }

                // Parse session info
                if (dataSet.Tables.Contains("SESSION_INFO"))
                {
                    var sessionTable = dataSet.Tables["SESSION_INFO"];
                    if (sessionTable.Rows.Count > 0)
                    {
                        response.SessionId = sessionTable.Rows[0]["SESSION_ID"]?.ToString() ?? "";
                        response.TransactionId = sessionTable.Rows[0]["TRANSACTION_ID"]?.ToString() ?? "";
                    }
                }

                // Determine if verification was successful
                response.IsVerified = response.StatusCode == "100" || response.StatusCode == "SUCCESS";

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing NADRA response");
                return new NadraParsedResponse
                {
                    StatusCode = "PARSE_ERROR",
                    Message = $"Failed to parse response: {ex.Message}",
                    IsVerified = false
                };
            }
        }

        private async Task<long> SaveBiometricVerificationAsync(NadraBiometricRequest request, string rawResponse, NadraParsedResponse parsedResponse)
        {
            try
            {
                // Check if verification already exists
                var existingVerification = await _context.BiometricVerifications
                    .FirstOrDefaultAsync(x =>
                        x.PartyId == request.PartyId &&
                        x.EStampMainId == request.TaskId &&
                        x.VERIFICATION_TYPE == VERIFICATION_TYPE.Biometric);

                // Create JSON data for storage
                var jsonData = System.Text.Json.JsonSerializer.Serialize(new
                {
                    StatusCode = parsedResponse.StatusCode,
                    Message = parsedResponse.Message,
                    IsVerified = parsedResponse.IsVerified,
                    CitizenName = parsedResponse.CitizenName,
                    FatherName = parsedResponse.FatherName,
                    DateOfBirth = parsedResponse.DateOfBirth,
                    Gender = parsedResponse.Gender,
                    Address = parsedResponse.Address,
                    SessionId = parsedResponse.SessionId,
                    TransactionId = parsedResponse.TransactionId,
                    VerificationDateTime = DateTime.UtcNow,
                    RawResponse = rawResponse
                });

                if (existingVerification != null)
                {
                    // Update existing record
                    existingVerification.Data = jsonData;
                    existingVerification.UpdatedAt = DateTime.UtcNow;
                    existingVerification.UpdatedBy = _authenticatedUser.UserId;

                    await _context.SaveChangesAsync();
                    return existingVerification.Id;
                }
                else
                {
                    // Create new record
                    var verification = new BiometricVerification
                    {
                        Id = _context.GetNextValueOfSequence(ApplicationDbContext.BiometricSequence),
                        PartyId = request.PartyId,
                        EStampMainId = request.TaskId,
                        VERIFICATION_TYPE = VERIFICATION_TYPE.Biometric,
                        Data = jsonData,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _authenticatedUser.UserId
                    };

                    _context.BiometricVerifications.Add(verification);
                    await _context.SaveChangesAsync();

                    return verification.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving biometric verification to database");
                throw;
            }
        }

        private async Task SafeCloseClient(BioVeriSysGenericClient? client)
        {
            if (client == null) return;

            try
            {
                if (client.State == CommunicationState.Faulted)
                {
                    _logger.LogWarning("WCF client is in faulted state, aborting connection");
                    client.Abort();
                }
                else if (client.State == CommunicationState.Opened)
                {
                    _logger.LogDebug("Closing WCF client connection");
                    await client.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error occurred while closing NADRA WCF client, aborting connection");
                try
                {
                    client.Abort();
                }
                catch (Exception abortEx)
                {
                    _logger.LogWarning(abortEx, "Error occurred while aborting NADRA WCF client");
                }
            }
        }

        private NadraBiometricResponse CreateErrorResponse(string errorMessage)
        {
            return new NadraBiometricResponse
            {
                Success = false,
                StatusCode = "ERROR",
                Message = errorMessage,
                IsVerified = false,
                VerificationDateTime = DateTime.UtcNow,
                NadraBiometricData = System.Text.Json.JsonSerializer.Serialize(new
                {
                    StatusCode = "ERROR",
                    Message = errorMessage,
                    IsVerified = false,
                    VerificationDateTime = DateTime.UtcNow
                })
            };
        }
    }

    // Supporting classes
    public class NadraBiometricRequest
    {
        public long CNIC { get; set; }
        public long PartyId { get; set; }
        public long TaskId { get; set; }
        public string FingerTemplate { get; set; } = string.Empty;
    }

    public class NadraBiometricResponse
    {
        public bool Success { get; set; }
        public string StatusCode { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string SessionId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public bool IsVerified { get; set; }
        public string CitizenName { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public string DateOfBirth { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public DateTime VerificationDateTime { get; set; }
        public long BiometricVerificationId { get; set; }
        public string RawResponse { get; set; } = string.Empty;
        public string NadraBiometricData { get; set; } = string.Empty;
    }

    public class NadraParsedResponse
    {
        public string StatusCode { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string SessionId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public bool IsVerified { get; set; }
        public string CitizenName { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public string DateOfBirth { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
    }

    /// <summary>
    /// LRMIS-style verification result following LRMIS patterns
    /// </summary>
    public class LrmisVerificationResult
    {
        public long CNIC { get; set; }
        public string StatusCode { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string SessionId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public bool IsVerified { get; set; }
        public string CitizenName { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public string DateOfBirth { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int FingerCount { get; set; }
        public DateTime VerificationDateTime { get; set; }
        public long BiometricVerificationId { get; set; }
        public DataSet? RawDataSet { get; set; }
    }
}
