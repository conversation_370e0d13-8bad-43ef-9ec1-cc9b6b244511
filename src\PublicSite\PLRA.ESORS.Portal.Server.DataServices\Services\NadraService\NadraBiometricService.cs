using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Portal.ServiceContracts;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.Server.Data.Entities;
using System.Data;

namespace PLRA.ESORS.Portal.Server.DataServices.Services.NadraService
{
    /// <summary>
    /// Simplified NADRA Biometric Service following official NADRA documentation
    /// </summary>
    public interface INadraBiometricApiService
    {
        Task<NadraBiometricResponse> VerifyBiometricAsync(NadraBiometricRequest request);
    }

    public class NadraBiometricApiService : INadraBiometricApiService
    {
        private readonly NadraSettings _nadraSettings;
        private readonly ILogger<NadraBiometricApiService> _logger;
        private readonly ApplicationDbContext _context;
        private readonly INadraApiService _nadraApiService;
        private readonly IErrorLoggingService _errorLoggingService;

        public NadraBiometricApiService(
            ILogger<NadraBiometricApiService> logger,
            ApplicationDbContext context,
            INadraApiService nadraApiService,
            IErrorLoggingService errorLoggingService,
            IOptions<NadraSettings> nadraSettings)
        {
            _logger = logger;
            _context = context;
            _nadraApiService = nadraApiService;
            _errorLoggingService = errorLoggingService;
            _nadraSettings = nadraSettings.Value;
        }

        /// <summary>
        /// NADRA biometric verification using official NADRA response format
        /// </summary>
        public async Task<NadraBiometricResponse> VerifyBiometricAsync(NadraBiometricRequest request)
        {
            try
            {
                // Call NADRA verification service
                var dataSet = await _nadraApiService.VerifyFingerPrintAsync(request);

                if (dataSet == null || dataSet.Tables.Count == 0)
                {
                    _logger.LogWarning("Empty or null dataset received from NADRA for CNIC: {CNIC}", request.CNIC);
                    return CreateErrorResponse("EMPTY_RESPONSE", "No response received from NADRA service");
                }

                // Parse NADRA response according to official documentation
                var response = ParseNadraDataSetResponse(dataSet, request.CNIC);


                return response;
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogExternalServiceErrorAsync(
                    nameof(NadraBiometricApiService),
                    nameof(VerifyBiometricAsync),
                    ex,
                    "NADRA Biometric Service",
                    "NADRA_SERVICE"
                );

                _logger.LogError(ex, "Error during NADRA verification for CNIC: {CNIC}", request.CNIC);
                return CreateErrorResponse("SERVICE_ERROR", $"Verification failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Parse NADRA DataSet response according to official documentation
        /// </summary>
        private NadraBiometricResponse ParseNadraDataSetResponse(DataSet dataSet, long cnic)
        {
            var response = new NadraBiometricResponse
            {
                CitizenNumber = cnic.ToString(),
                VerificationDateTime = DateTime.UtcNow
            };

            try
            {
                // Parse RESPONSE_STATUS table for Code and Message
                if (dataSet.Tables.Contains("RESPONSE_STATUS"))
                {
                    var statusTable = dataSet.Tables["RESPONSE_STATUS"];
                    if (statusTable.Rows.Count > 0)
                    {
                        response.Code = statusTable.Rows[0]["CODE"]?.ToString() ?? "";
                        response.Message = statusTable.Rows[0]["MESSAGE"]?.ToString() ?? "";
                    }
                }

                // Parse SESSION_ID if available
                if (dataSet.Tables.Contains("SESSION"))
                {
                    var sessionTable = dataSet.Tables["SESSION"];
                    if (sessionTable.Rows.Count > 0)
                    {
                        response.SessionId = sessionTable.Rows[0]["SESSION_ID"]?.ToString() ?? "";
                    }
                }

                // Parse FINGER table for available finger indexes
                if (dataSet.Tables.Contains("FINGER"))
                {
                    var fingerTable = dataSet.Tables["FINGER"];
                    var fingerIndexes = new List<string>();
                    
                    foreach (DataRow row in fingerTable.Rows)
                    {
                        var fingerIndex = row["FINGER_INDEX"]?.ToString();
                        if (!string.IsNullOrEmpty(fingerIndex))
                        {
                            fingerIndexes.Add(fingerIndex);
                        }
                    }
                    
                    response.FingerIndex = fingerIndexes.ToArray();
                }

                _logger.LogInformation("Parsed NADRA response - Code: {Code}, Message: {Message}, Verified: {IsVerified}",
                    response.Code, response.Message, response.IsVerified);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error parsing NADRA DataSet response for CNIC: {CNIC}", cnic);
                response.Code = "PARSE_ERROR";
                response.Message = "Error parsing NADRA response";
            }

            return response;
        }

        /// <summary>
        /// Create error response using NADRA documentation format
        /// </summary>
        private NadraBiometricResponse CreateErrorResponse(string code, string message)
        {
            return new NadraBiometricResponse
            {
                Code = code,
                Message = message,
                VerificationDateTime = DateTime.UtcNow
            };
        }
    }

    // NADRA Request/Response Models based on official documentation
    public class NadraBiometricRequest
    {
        public long CNIC { get; set; }
        public long PartyId { get; set; }
        public long TaskId { get; set; }
        public string FingerTemplate { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// NADRA Biometric Response based on official NADRA documentation
    /// Response Interface Fields: Code, Message, Session ID, Citizen Number, Finger Index
    /// </summary>
    public class NadraBiometricResponse
    {
        /// <summary>
        /// Code response to request (String, Length: 3, Mandatory)
        /// Example: "100"
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Message explaining the response code (String, Length: 256, Mandatory)
        /// Example: "Successful"
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Unique session id for using BioVeriSys (String, Length: 19, Mandatory)
        /// This session ID will be used for the billing to the service provider
        /// Example: "400110000010371 9929"
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// Contains Primary/Sender citizen number (String, Length: 13, Mandatory)
        /// Example: "6110119876547"
        /// </summary>
        public string CitizenNumber { get; set; } = string.Empty;

        /// <summary>
        /// List of available finger indexes (String[], Non Mandatory)
        /// Where verification result is not successful
        /// Mandatory if specified in the agreement
        /// Example: "1,2,4,6"
        /// </summary>
        public string[] FingerIndex { get; set; } = Array.Empty<string>();

        // Additional fields for internal processing
        public bool IsVerified => Code == "100";
        public DateTime VerificationDateTime { get; set; } = DateTime.UtcNow;
        public long BiometricVerificationId { get; set; }
    }
}
