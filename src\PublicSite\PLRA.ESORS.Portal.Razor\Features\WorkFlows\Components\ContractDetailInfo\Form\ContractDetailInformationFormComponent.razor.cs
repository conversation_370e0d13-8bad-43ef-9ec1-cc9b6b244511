﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Framework.ViewModels;
using PLRA.ESORS.Portal.ServiceContracts.SelectList;

namespace PLRA.ESORS.Portal.Razor.Features.WorkFlows.Components.ContractDetailInfo.Form
{
    public partial class ContractDetailInformationFormComponent
    {
        [Parameter, EditorRequired]
        public long TaskId { get; set; }

        [Parameter, EditorRequired]
        public int Step { get; set; }

        [Parameter , EditorRequired]
        public CONTRACT_TYPE CONTRACT_TYPE { get; set; }

        [Parameter, EditorRequired]
        public CONTRACT_CLAUSES CONTRACT_CLAUSES { get; set; }

        public List<SelectListItem> ListOfContractScopes { get; set; } = new();
        private List<SelectListItem> Districts { get; set; } = new();
        private List<SelectListItem> Tehsils { get; set; } = new();

        protected override async Task OnInitializedAsync()
        {
            Id = TaskId;
            
            await base.OnInitializedAsync();
            if (SelectedItem != null)
            {
                SelectedItem.Id = SelectedItem.Id ?? 0;
                SelectedItem.CONTRACT_TYPE = CONTRACT_TYPE;
                SelectedItem.CONTRACT_CLAUSES = CONTRACT_CLAUSES;
                SelectedItem.From = SelectedItem.From == null || SelectedItem.From == DateTime.MinValue ? DateTime.Today: SelectedItem.From;
                SelectedItem.To = SelectedItem.To == null || SelectedItem.To == DateTime.MinValue ? DateTime.Today.AddMonths(1) : SelectedItem.To;
                SelectedItem.EStampMainId = TaskId;
            }
        }

        protected override async Task LoadSelectLists(IServiceScope scope)
        {
            ArgumentNullException.ThrowIfNull(SelectedItem);
            var service = scope.ServiceProvider.GetRequiredService<ISelectListDataService>();
            Districts = await service.GetDistricts();
            if (Districts != null)
            {
                int districtId = (int)(SelectedItem.DistrictId > 0 ? SelectedItem.DistrictId : Districts.FirstOrDefault()?.IntValue.GetValueOrDefault()!);
                Tehsils = await service.GetTehsilByDistrictId(districtId);
            }
            ListOfContractScopes = await service.GetContractScopes();
        }

       
    }
}