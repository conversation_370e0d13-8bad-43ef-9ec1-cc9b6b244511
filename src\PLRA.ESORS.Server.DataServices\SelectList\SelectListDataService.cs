﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using PLRA.ESORS.Framework.ViewModels;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.Server.DataServices.Services.TerritoryService;
using PLRA.ESORS.ServiceContracts;
using PLRA.ESORS.ServiceContracts.SelectList;
using System.Text.Json;

namespace PLRA.ESORS.Server.DataServices.SelectList
{
    public class SelectListDataService : ISelectListDataService
    {
        private readonly IDistributedCache _cache;
        private readonly IStaticTerritoryService _staticTerritoryService;
        private readonly ApplicationDbContext _context;
        private readonly IErrorLoggingService _errorLoggingService;

        public SelectListDataService(ApplicationDbContext context, IStaticTerritoryService staticTerritoryService, IDistributedCache cache, IErrorLoggingService errorLoggingService)
        {
            _context = context;
            _staticTerritoryService = staticTerritoryService;
            _cache = cache;
            _errorLoggingService = errorLoggingService;
        }

       
        public async Task<List<SelectListItem>> GetParentPurposes()
        {
            try
            {
                var purposes = await _context.Purposes
                        .Where(x => x.Type == Framework.Enums.PURPOSE_TYPE.Parent && x.IsActive)
                        .OrderBy(x => x.Name_En)
                        .Select(x => new SelectListItem(x.Id, x.Name_En, false))
                        .ToListAsync();
                return purposes;
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogDatabaseErrorAsync(
                    nameof(SelectListDataService),
                    nameof(GetParentPurposes),
                    ex,
                    "SELECT * FROM Purposes WHERE Type = 'Parent' AND IsActive = 1"
                );
                throw;
            }
        }

        public async Task<List<SelectListItem>> GetSubCategoryPurposes(int parentId)
        {
            try
            {
                var purposes = await _context.Purposes
                        .Where(x => x.ParentId == parentId && x.Type == Framework.Enums.PURPOSE_TYPE.SubCategory && x.IsActive)
                        .OrderBy(x => x.Name_En)
                        .Select(x => new SelectListItem(x.Id, x.Name_En, false))
                        .ToListAsync();
                return purposes;
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogDatabaseErrorAsync(
                    nameof(SelectListDataService),
                    nameof(GetSubCategoryPurposes),
                    ex,
                    "SELECT * FROM Purposes WHERE ParentId = @parentId AND Type = 'SubCategory' AND IsActive = 1",
                    new { parentId }
                );
                throw;
            }
        }

        public async Task<List<SelectListItem>> GetWorkflows()
        {
            var workflows = await _context.WorkFlows
                    .Where(x => x.IsActive)
                    .OrderBy(x => x.Name)
                    .Select(x => new SelectListItem(x.Id, x.Name, false))
                    .ToListAsync();
            return workflows;
        }

        public async Task<List<Territory>?> GetDistrictsAndTehsils()
        {
            var districtsAndTehsils = await _cache.GetStringAsync($"GetDistrictsAndTehsils");
            if (districtsAndTehsils != null)
            {
                return await Task.FromResult(JsonSerializer.Deserialize<List<Territory>>(districtsAndTehsils)!);
            }
            else
            {
                var response = await _staticTerritoryService.GetDistrictAndTehsils();
                if (response != null && response.Data != null)
                {
                    await _cache.SetStringAsync($"GetDistrictsAndTehsils", JsonSerializer.Serialize(response.Data));
                    return response.Data;
                }
                return null;
            }
        }

        public async Task<List<SelectListItem>> GetDistricts()
        {
            var districts = await _cache.GetStringAsync($"GetDistricts");
            if (districts != null)
            {
                return await Task.FromResult(JsonSerializer.Deserialize<List<SelectListItem>>(districts)!);
            }
            else
            {
                var newDistricts = new List<SelectListItem>();
                List<Territory>? response = null;
                var districtsAndTehsils = await _cache.GetStringAsync($"GetDistrictsAndTehsils");
                if (districtsAndTehsils != null)
                {
                    response = JsonSerializer.Deserialize<List<Territory>>(districtsAndTehsils)!;
                }
                else
                {
                    response = await GetDistrictsAndTehsils();
                }

                if (response != null)
                {
                    foreach (var district in response)
                    {
                        newDistricts.Add(new SelectListItem(district.DistrictId, district.DistrictName));
                    }
                }

                if (newDistricts.Count > 0)
                    await _cache.SetStringAsync($"GetDistricts", JsonSerializer.Serialize(newDistricts));

                return newDistricts;
            }
        }

        public async Task<List<SelectListItem>> GetTehsilByDistrictId(int districtId)
        {

            var tehsils = await _cache.GetStringAsync($"GetTehsils_{districtId}");
            if (tehsils != null)
            {
                return await Task.FromResult(JsonSerializer.Deserialize<List<SelectListItem>>(tehsils)!);
            }
            else
            {

                var newTehsils = new List<SelectListItem>();

                var response = await GetDistrictsAndTehsils();
                if (response != null)
                {
                    var tehsilsList = response.FirstOrDefault(x => x.DistrictId == districtId)?.Tehsils;
                    if (tehsilsList != null)
                    {
                        foreach (var tehsil in tehsilsList)
                        {
                            newTehsils.Add(new SelectListItem(tehsil.TehsilId, tehsil.TehsilName));
                        }
                    }

                }
                if (newTehsils.Count > 0)
                    await _cache.SetStringAsync($"GetTehsils_{districtId}", JsonSerializer.Serialize(newTehsils));

                return newTehsils;
            }
        }

        public async Task<List<SelectListItem>> GetMauzaByTehsilId(int tehsilId)
        {
            var mauzas = await _cache.GetStringAsync($"GetMauzas_{tehsilId}");
            if (mauzas != null)
            {
                return await Task.FromResult(JsonSerializer.Deserialize<List<SelectListItem>>(mauzas)!);
            }
            else
            {

                var newMauzas = new List<SelectListItem>();
                List<Mauza>? response = null;
                var mauzasByTehsil = await _cache.GetStringAsync($"GetMauzaByTehsilId_{tehsilId}");
                if (mauzasByTehsil != null)
                {
                    response = JsonSerializer.Deserialize<List<Mauza>>(mauzasByTehsil)!;
                }
                else
                {
                    var result = await _staticTerritoryService.GetMauzaByTehsilId(tehsilId);
                    response = result?.Data;
                }


                if (response != null)
                {
                    foreach (var mauza in response)
                    {
                        newMauzas.Add(new SelectListItem(mauza.MauzaId, mauza.MauzaName));
                    }
                }

                if (newMauzas.Count > 0)
                    await _cache.SetStringAsync($"GetMauzas_{tehsilId}", JsonSerializer.Serialize(newMauzas));

                return newMauzas;
            }
        }
    }
}
