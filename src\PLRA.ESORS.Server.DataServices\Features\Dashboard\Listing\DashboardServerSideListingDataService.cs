﻿
using PLRA.ESORS.Framework;
using PLRA.ESORS.Server.DataServices;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.ServiceContracts;
using PLRA.ESORS.ServicesContracts.Features.Dashboard;

public class DashboardServerSideListingDataService : ServerSideListingDataService<DashboardListingBusinessObject, DashboardFilterBusinessObject>, IDashboardListingDataService
{

	private readonly ApplicationDbContext _context;
	private readonly IErrorLoggingService _errorLoggingService;

	public DashboardServerSideListingDataService (ApplicationDbContext context, IErrorLoggingService errorLoggingService)
	{
		_context = context;
		_errorLoggingService = errorLoggingService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public override IQueryable<DashboardListingBusinessObject> GetQuery(DashboardFilterBusinessObject filterBusinessObject)
	{
		return Array.Empty<DashboardListingBusinessObject>().AsQueryable();
	}
}
