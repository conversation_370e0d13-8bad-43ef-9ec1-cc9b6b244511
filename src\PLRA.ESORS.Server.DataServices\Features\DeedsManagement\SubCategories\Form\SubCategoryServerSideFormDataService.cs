using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PLRA.ESORS.Framework;
using PLRA.ESORS.Framework.Enums;
using PLRA.ESORS.Server.Data.Data;
using PLRA.ESORS.Server.Data.Entities;
using PLRA.ESORS.ServiceContracts;
using PLRA.ESORS.ServiceContracts.Models;
using PLRA.ESORS.ServicesContracts.Features.ParentCategory;
using PLRA.ESORS.ServicesContracts.Features.SubCategory;
namespace PLRA.ESORS.Server.DataServices.Features.SubCategory;
public class SubCategoryServerSideFormDataService : ISubCategoryFormDataService
{
    private readonly ResourceSettings _resources;
    private readonly ApplicationDbContext _context;
    private readonly IErrorLoggingService _errorLoggingService;

    public SubCategoryServerSideFormDataService (ApplicationDbContext context, IOptions<ResourceSettings> resources, IErrorLoggingService errorLoggingService)
    {
        _context = context;
        _resources = resources.Value;
        _errorLoggingService = errorLoggingService;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<int> SaveAsync(SubCategoryFormBusinessObject formBusinessObject)
	{
        var existedPurpose = await _context.Purposes.FirstOrDefaultAsync(x => x.Name_En == formBusinessObject.Name_En && formBusinessObject.Id.GetValueOrDefault() == 0 && x.Type == PURPOSE_TYPE.SubCategory);
        if (existedPurpose != null) throw new Exception("Record already exists");

        var purpose = await _context.Purposes.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id.GetValueOrDefault());
        if (purpose == null)
        {
            purpose = new Purpose
            {
                Id = _context.GetNextValueOfSequence(ApplicationDbContext.PurposeSequence),
                CreatedAt = DateTime.UtcNow
            };

            _context.Purposes.Add(purpose);
        }
        purpose.ParentId = formBusinessObject.ParentId;
        purpose.Name_En = formBusinessObject.Name_En;
        purpose.Name_Ur = formBusinessObject.Name_Ur;
        purpose.Description_En = formBusinessObject.Description_En;
        purpose.Description_Ur = formBusinessObject.Description_Ur;
        purpose.IsActive = formBusinessObject.IsActive;
        purpose.Type = PURPOSE_TYPE.SubCategory;
        purpose.UpdatedAt = DateTime.UtcNow;

        if (formBusinessObject.FileInfo != null && string.IsNullOrWhiteSpace(formBusinessObject.FileInfo.Url))
        {
            var imageExt = Path.GetExtension(formBusinessObject.FileInfo.FileName);
            var imageFileName = Guid.NewGuid().ToString();
            var directoryPath = $"{_resources.Path}\\media";

            if (!Directory.Exists(directoryPath))
                Directory.CreateDirectory(directoryPath);

            var filePath = Path.Combine(directoryPath, $"{imageFileName}{imageExt}");

            await File.WriteAllBytesAsync(filePath, formBusinessObject.FileInfo.FileBytes);

            purpose.IconPath = $"/media/{imageFileName}{imageExt}";
        }

        return await _context.SaveChangesAsync();
    }
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<SubCategoryFormBusinessObject?> GetItemByIdAsync(int id)
	{
        var purpose = await _context.Purposes
           .Where(x => x.Id == id)
           .Select(p => new SubCategoryFormBusinessObject()
           {
               Id = p.Id,
               ParentId = p.ParentId.GetValueOrDefault(),
               Name_En = p.Name_En,
               Name_Ur = p.Name_Ur,
               Description_En = p.Description_En,
               Description_Ur = p.Description_Ur,
               FileInfo = new FileInformation { Url = $"{_resources.Url}{p.IconPath}" },
               IsActive = p.IsActive,
           }).FirstOrDefaultAsync();

        return purpose;
    }
}
